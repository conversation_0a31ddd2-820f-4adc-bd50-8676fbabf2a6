[tool.poetry]
name = "linebot"
version = "0.1.0"
description = "A linebot backend"
authors = ["AIBrainlab"]
license = "MIT"
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
django = "^5.0.3"
line-bot-sdk = "3.14.4"
redis = "^5.0.3"
python-dotenv = "^1.0.1"
psycopg = {extras = ["binary"], version = "^3.1.18"}
langserve = {extras = ["all"], version = "^0.0.51"}
langchain = "^0.1.13"
uvicorn = "^0.29.0"
djangorestframework = "^3.15.1"
drf-yasg = "^1.21.7"
djangorestframework-simplejwt = "^5.3.1"
celery = "^5.4.0"
django-cors-headers = "^4.4.0"
django-filter = "^24.3"
azure-storage-blob = "^12.24.0"
channels = {extras = ["daphne"], version = "^4.2.0"}
channels-redis = "^4.2.1"
websockets = "^14.2"
django-htmx = "^1.22.0"
azure-cli = "^2.69.0"
pytest-django = "^4.10.0"
python-decouple = "^3.8"
pillow = "^11.2.1"
reportlab = "^4.4.2"
openpyxl = "^3.1.5"
fakeredis = "^2.30.1"
drf-excel = "^2.5.3"
bleach = "^6.0.0"
html5lib = "^1.1"
validators = "^0.35.0"

[tool.poetry.group.test.dependencies]
pytest = "^8.0.2"
pytest-depends = "^1.0.1"
pytest-cov = "^4.1.0"
pytest-order = "^1.2.0"
pytest-mock = "^3.12.0"
pytest-html = "^4.1.1"

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "devproject.settings"
python_files = ["tests*.py"]
addopts = "-v"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"


[tool.poe.tasks.run]
cmd     = "python manage.py runserver 0.0.0.0:8001"
envfile = ['.env']

[tool.poe.tasks.shell]
cmd     = "./manage.py shell"
envfile = ['.env']

[tool.poe.tasks.test]
cmd     = "./manage.py test"
envfile = ['.env']

# [tool.poe.tasks.test]
# # DJANGO_SETTINGS_MODULE = "devproject.settings"
# cmd     = "pytest"
# envfile = ['.env']

[tool.poe.tasks.migrate]
cmd     = "python manage.py migrate"
envfile = ['.env']

[tool.poe.tasks.showmigrations]
cmd     = "python manage.py showmigrations"
envfile = ['.env']

[tool.poe.tasks.makemigrations]
cmd     = "python manage.py makemigrations"
envfile = ['.env']

[tool.poe.tasks.help]
cmd     = "python manage.py help"
envfile = ['.env']

[tool.poe.tasks.initdb]
cmd     = "python manage.py initialize_database_instances"
envfile = ['.env']

[tool.poe.tasks.inactive_tickets]
cmd     = "python manage.py inactive_tickets"
envfile = ['.env']

[tool.poe.tasks.backup_postgres_database]
cmd     = "python manage.py backup_postgres_database"
envfile = ['.env']

[tool.poe.tasks.start_celery_worker]
# cmd     = "celery -A devproject worker -l info"
cmd = "celery -A devproject worker -Q celery,default -l info -n default_worker@%h"
envfile = ['.env']

[tool.poe.tasks.start_celery_beat]
# cmd     = "celery -A devproject beat -l info"
cmd = "celery -A devproject beat -l info"
envfile = ['.env']

[tool.poe.tasks.start_celery_line]
# cmd     = "celery -A devproject worker -Q line -l info"
cmd = "celery -A devproject worker -Q line -l info -n line_worker@%h"
envfile = ['.env']

[tool.poe.tasks.start_celery_websocket]
# cmd     = "celery -A devproject worker -Q websocket -l info"
cmd = "celery -A devproject worker -Q websocket -l info -n websocket_worker@%h"
envfile = ['.env']
