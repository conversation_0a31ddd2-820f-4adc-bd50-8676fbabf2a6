import os
from datetime import datetime
from django.db.models import <PERSON><PERSON><PERSON>, <PERSON>query, F, <PERSON>, Value, <PERSON><PERSON><PERSON><PERSON>, Case, When
from django.conf import settings
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from django_filters import rest_framework as filters
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from rest_framework import generics, mixins, status
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import (
    IsAuthenticated,
    AllowAny,
    IsAuthenticatedOrReadOnly,
    IsAdminUser,
)

from devproject.views import StandardResultsSetPagination
from user.permissions import IsOwnerOrReadOnly, IsHigherThanAgentOrTicketOwner, TicketOwnershipPermission
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter

from .models import <PERSON><PERSON><PERSON><PERSON>, Ticket, Status, Message, StatusLog, OwnerLog, Ticket<PERSON>op<PERSON>, Ticket<PERSON><PERSON><PERSON>, TicketAnalysis, AnalysisHighlight
from .serializers import OwnerLogBasicSerializer, TicketSerializer, StatusSerializer, MessageSerializer, StatusLogSerializer, OwnerLogSerializer, TicketTopicSerializer, TicketPrioritySerializer, TicketAnalysisSerializer, EnhancedTicketSerializer
# TODO - Clean this
# from .utils import get_last_n_messages_formatted, json_list_to_string, list_ticket_message_history, update_ticket_status
# from .utils import get_last_n_messages_formatted, update_ticket_status, transfer_ticket_owner, check_customer_tickets
from .utils import get_last_n_messages_formatted, update_ticket_status, check_customer_tickets, transfer_ticket_owner_v2
from .mixins import TicketLogger
from .enums.request_type import RequestType

from user.models import User, Role, UserRole
from user.serializers import UserBasicSerializer, UserSerializer
from user.services import UserStatusService
from user.utils.common import get_a_random_user_from_role
from customer.models import Customer, Interface
# from linechatbot.line import send_line_message_to_line_user
# from linechatbot.utils import send_line_message_to_line_user
from devproject.utils.utils import LoggingMixin

from ticket.serializers import WebSocketsMessageSerializer
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.utils import timezone

from linebot.v3.messaging import Configuration
from linebot.v3.messaging import (
    ApiClient,
    MessagingApi,
    TextMessage
)

# def _check_environment():
#     if "LINE_CHANNEL_SECRET" not in os.environ:
#         raise EnvironmentError(f"Expect to have `LINE_CHANNEL_SECRET` variable in environment.")
#     if "LINE_ACCESS_TOKEN" not in os.environ:
#         raise EnvironmentError(f"Expect to have `LINE_ACCESS_TOKEN` variable in environment.")

# _check_environment()

# # Set initial parameters for the app workflow
# _LINE_CHANNEL_SECRET = os.environ["LINE_CHANNEL_SECRET"]
# _LINE_ACCESS_TOKEN = os.environ["LINE_ACCESS_TOKEN"]
# configuration = Configuration(access_token=_LINE_ACCESS_TOKEN)
# configuration.proxy = os.environ["https_proxy"] if 'https_proxy' in os.environ else ''

class TicketCreationView(
    LoggingMixin, generics.GenericAPIView, mixins.CreateModelMixin
    # LoggingMixin, TicketLogger, generics.GenericAPIView, mixins.CreateModelMixin
):
    serializer_class = TicketSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Ticket.objects.all()

    def perform_create(self, serializer, selected_status, selected_owner):
        user = self.request.user
        created_ticket = serializer.save(
            status_id=selected_status,
            owner_id=selected_owner,
            created_by=user
        )
        owner = created_ticket.owner_id
        status = created_ticket.status_id
        
        # Create an Message instance
        Message.objects.create(
            ticket_id=created_ticket,
            message = f"Test message of Ticket {created_ticket.id}"
        )

        # Create an OwnerLog instance
        OwnerLog.objects.create(
            ticket_id=created_ticket,
            owner_id=owner,
            created_by=user
        )

        # Create an StatusLog instance
        StatusLog.objects.create(
            ticket_id=created_ticket,
            status_id=status,
            created_by=user
        )
        return super().perform_create(serializer)

    def post(self, request: Request, *args, **kwargs):
        # Get a instances' features
        customer_id = request.data.get('customer_id')
        status_id = request.data.get('status_id')
        owner_id = request.data.get('owner_id')
        user = self.request.user
        
        if status_id is None:
            status_id = Status.objects.get(name='open').id # Default Open status

        if not customer_id or not status_id or not owner_id:
            return Response({"message": "Both 'customer_id', 'status_id' and 'owner_id' are required."}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            selected_customer = Customer.objects.get(customer_id=customer_id)
        except Customer.DoesNotExist:
            return Response({"message": "The specified customer_id is not valid."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            selected_status = Status.objects.get(id=status_id)
        except Status.DoesNotExist:
            return Response({"message": "The specified status_id is not valid."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            selected_owner = User.objects.get(id=owner_id)
        except User.DoesNotExist:
            return Response({"message": "The specified owner_id is not valid."}, status=status.HTTP_400_BAD_REQUEST)
        
        if selected_status.name == "default":
            return Response({"message": "Cannot config a ticket's status to Default status"}, status=status.HTTP_400_BAD_REQUEST)
        

        serializer = self.get_serializer(data=request.data)
        # import pdb; pdb.set_trace()
        
        if serializer.is_valid():
            self.perform_create(serializer, selected_status, selected_owner)
            response = {"message": "Ticket Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        else:
            print(f"Serializer errors: {serializer.errors}")
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class TicketTransferOwnerView(
    # LoggingMixin, TicketLogger, generics.GenericAPIView, mixins.UpdateModelMixin
    LoggingMixin, generics.GenericAPIView, mixins.UpdateModelMixin
):
    """
    Any employee can transferred (take over) a ticket which owner is System (Chatbot) via manually transfer on a web page
    Only users with supervisor role or the ticket's owner can transfer a ticket that already assigned to a employee to themselves or other users
    """
    serializer_class = TicketSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, TicketOwnershipPermission]
    # permission_classes = [IsHigherThanAgentOrTicketOwner]
    queryset = Ticket.objects.all()

    def has_permission(self, request, view):
        # Check permissions before processing the request
        ticket_id = self.kwargs.get('pk')
        print(f"TicketTransferOwnerView's ticket_id - {ticket_id}")
        try:
            ticket = Ticket.objects.get(pk=ticket_id)
            return TicketOwnershipPermission().has_object_permission(request, self, ticket)
        except Ticket.DoesNotExist:
            return False

    def perform_update(self, serializer, new_owner):
        ticket = self.get_object()
        transfer_ticket_owner_v2(ticket, new_owner, self.request.user)
        
        # Only update status if the transfer was successful
        assigned_status = Status.objects.get(name='assigned')
        if ticket.status_id != assigned_status:
            update_ticket_status(ticket, assigned_status, self.request.user)

    def put(self, request: Request, *args, **kwargs):
        # Get a ticket with its primary key
        try:
            ticket_id = kwargs.get('pk')
            instance = Ticket.objects.get(pk=ticket_id)
        except Ticket.DoesNotExist:
            return Response(
                {"message": "Ticket not found."}, 
                status=status.HTTP_404_NOT_FOUND
            )
        customer = instance.customer_id
        owner = instance.owner_id
        
        # Check: Is the ticket's owner is System ?
        owner_role = UserRole.objects.get(user_id=owner).role_id

        # import pdb; pdb.set_trace()
        
        # Commented out to allow Agent to transfer ticket to self or others
        # if owner_role != Role.objects.get(name="System"):
        #     # Check: a user's permissions
        #     if not self.has_permission(request, self):
        #         return Response(
        #             {"message": "You don't have permission to transfer this ticket."},
        #             status=status.HTTP_403_FORBIDDEN
        #         )

        new_owner_id = request.data.get('new_owner_id')
        if not ticket_id or not new_owner_id:
            return Response(
                {"message": "Both 'ticket_id' and 'new_owner_id' are required."}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        if instance.status_id.name == "closed":
            return Response(
                {"message": "Tickets with 'closed' status cannot be transferred."}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if instance.owner_id.id == new_owner_id:
            return Response(
                {"message": "Cannot transfer a ticket to the current ticket's owner."}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            new_owner = User.objects.get(id=new_owner_id)
            # TODO - Check this
            # if not new_owner.line_user_id:
            #     ticket_interface = instance.ticket_interface.name
            #     return Response(
            #         {"message": f"The selected user does not have an assosicated {ticket_interface} account"}, 
            #         status=status.HTTP_400_BAD_REQUEST
            #     )
        except User.DoesNotExist:
            return Response(
                {"message": "The specified new_owner_id is not valid."}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Execute Transfer Ticket's owner process
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            self.perform_update(serializer, new_owner)
            response = {
                "message": "Ticket Updated Successfully", 
                "data": {**serializer.data, "old_owner": instance.owner_id.id}
            }

            # TODO - Add more possible interface here when there is an addition interface integrate with this app
            # Send a notify message to a ticket's customer according to a ticket's interface
            line_interface = Interface.objects.get(name='LINE')
            open_status = Status.objects.get(name="open")
            ticket_interface = instance.ticket_interface
            # TODO - Delete this
            print(f"TicketTransferOwnerView's ticket_interface - {ticket_interface}")

            # if ticket_interface == line_interface:
            #     # Send notification to the LINE group of a company with mention supervisor(s)
            #     send_line_message_to_line_user(
            #         ticket=instance,
            #         line_profile=new_owner.line_user_id,
            #         to_group=True,
            #     )

            #     if instance.status_id == open_status:
            #         # Send notification to a customer about ticket transfering 
            #         send_line_message_to_line_user(
            #             ticket=instance,
            #             line_profile=customer.line_user_id,
            #             to_customer=True
            #         )

            return Response(data=response, status=status.HTTP_200_OK)
        
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
# # Version 01
# class TicketChangeStatusView(
#     # LoggingMixin, TicketLogger, generics.GenericAPIView, mixins.UpdateModelMixin
#     LoggingMixin, generics.GenericAPIView, mixins.UpdateModelMixin
# ):
#     serializer_class = TicketSerializer
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated, TicketOwnershipPermission]
#     # permission_classes = [IsHigherThanAgentOrTicketOwner]

#     queryset = Ticket.objects.all()

#     def put(self, request: Request, *args, **kwargs):
#         # Get the ticket instance
#         try:
#             ticket_id = kwargs.get('pk')
#             instance = Ticket.objects.get(pk=ticket_id)
#         except Ticket.DoesNotExist:
#             return Response({"message": "Ticket not found."}, status=status.HTTP_404_NOT_FOUND)
        
#         # Check permissions for the specific object
#         self.check_object_permissions(request, instance)

#         new_status_id = request.data.get('new_status_id')

#         if not ticket_id or not new_status_id:
#             return Response({"message": "Both 'ticket_id' and 'new_status_id' are required."}, status=status.HTTP_400_BAD_REQUEST)

#         # Get the new status instance
#         try:
#             new_status = Status.objects.get(id=new_status_id)
#         except Status.DoesNotExist:
#             return Response({"message": "The specified new_status_id is not valid."}, status=status.HTTP_400_BAD_REQUEST)

#         # Check if the new status is not the default status
#         if new_status.name == "default":
#             return Response({"message": "No status can change to Default status"}, status=status.HTTP_400_BAD_REQUEST)

#         partial = kwargs.pop('partial', True)
#         serializer = self.get_serializer(instance, data=request.data, partial=partial)
#         if serializer.is_valid():
#             # Use the callable function to update the ticket status
#             update_ticket_status(instance, new_status, request.user)
#             response = {"message": "Ticket Updated Successfully", "data": {**serializer.data, "old_status": instance.status_id.id}}
#             return Response(data=response, status=status.HTTP_200_OK)
        
#         return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# Version 02 - Add conditions so that a concept of ONE NON-CLOSED TICKET PER ONE CUSTOMER is applied
class TicketChangeStatusView(
    # LoggingMixin, TicketLogger, generics.GenericAPIView, mixins.UpdateModelMixin
    LoggingMixin, generics.GenericAPIView, mixins.UpdateModelMixin
):
    serializer_class = TicketSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, TicketOwnershipPermission]
    # permission_classes = [IsHigherThanAgentOrTicketOwner]

    queryset = Ticket.objects.all()

    def put(self, request: Request, *args, **kwargs):
        # Get the ticket instance
        try:
            ticket_id = kwargs.get('pk')
            instance = Ticket.objects.get(pk=ticket_id)
        except Ticket.DoesNotExist:
            return Response({"message": "Ticket not found."}, status=status.HTTP_404_NOT_FOUND)
        new_status_id = request.data.get('new_status_id')

        # Check permissions for the specific object
        self.check_object_permissions(request, instance)

        if not ticket_id or not new_status_id:
            return Response({"message": "Both 'ticket_id' and 'new_status_id' are required."}, status=status.HTTP_400_BAD_REQUEST)

        # Get the new status instance
        try:
            new_status = Status.objects.get(id=new_status_id)
        except Status.DoesNotExist:
            return Response({"message": "The specified new_status_id is not valid."}, status=status.HTTP_400_BAD_REQUEST)

        # Check if the new status is not the default status
        if new_status.name == "default":
            return Response({"message": "No status can change to Default status"}, status=status.HTTP_400_BAD_REQUEST)

        # Check ONE NON-CLOSED TICKET PER ONE CUSTOMER concept
        customer_id = instance.customer_id.customer_id
        ticket_id = instance.id
        is_this_customer_latest_ticket = check_customer_tickets(
            customer_id=customer_id,
            ticket_id=ticket_id,
            is_this_customer_latest_ticket=True,
            new_ticket_status=new_status
        )

        if not is_this_customer_latest_ticket:
            return Response({"message": f"Ticket ID: {ticket_id}  is not the latest of a customer's ID: {customer_id}"}, status=status.HTTP_400_BAD_REQUEST)

        # Check whether Ticker's owner and new status is agreeable to each other
        # System with open (id=2), non-System with assigned (id=5)
        if instance.owner_id.id == 2 and new_status.id == 5:
            return Response({"message": "Cannot change a ticket's status to Assigned when a ticket's owner is System"}, status=status.HTTP_400_BAD_REQUEST)

        if instance.owner_id.id != 2 and new_status.id == 2:
            return Response({"message": "Cannot change a ticket's status to Open when a ticket's owner is non-System"}, status=status.HTTP_400_BAD_REQUEST)
            
        # Execute Change Ticket's status process
        partial = kwargs.pop('partial', True)
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            # Use the callable function to update the ticket status
            update_ticket_status(instance, new_status, request.user)
            response = {"message": "Ticket Updated Successfully", "data": {**serializer.data, "old_status": instance.status_id.id}}
            return Response(data=response, status=status.HTTP_200_OK)
        
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class TicketMessageHistoryView(
    LoggingMixin, APIView
):
    def post(self, request, *args, **kwargs):
        # Get customer_id and last_n_tickets from the request data
        customer_id = request.data.get('customer_id')
        last_n_tickets = request.data.get('last_n_tickets')
        last_n_messages = request.data.get('last_n_messages')

        if not customer_id:
            return Response({"message": "'customer_id' or 'last_n_tickets' are missing."}, status=status.HTTP_400_BAD_REQUEST)

        if last_n_tickets is not None and type(last_n_tickets) is not int and int(last_n_tickets) > 0:
            return Response({"message": "'last_n_tickets' should be positive integer or None."}, status=status.HTTP_400_BAD_REQUEST)

        if last_n_messages is not None and type(last_n_messages) is not int and int(last_n_messages) > 0:
            return Response({"message": "'last_n_messages' should be positive integer or None."}, status=status.HTTP_400_BAD_REQUEST)


        # Fetch the customer instance
        try:
            customer = Customer.objects.get(customer_id=customer_id)
        except Customer.DoesNotExist:
            return Response({"message": "Customer not found"}, status=status.HTTP_404_NOT_FOUND)

        print(f"TicketMessageHistoryView's last_n_messages - {last_n_messages}")
        print(f"TicketMessageHistoryView's last_n_tickets - {last_n_tickets}")

        # Fetch message history
        llm_input = get_last_n_messages_formatted(
            customer=customer,
            last_n_tickets=last_n_tickets,
            last_n_messages=last_n_messages
        )

        return Response(data=llm_input, status=status.HTTP_200_OK)


class TicketHistoryLogView(
    LoggingMixin, generics.GenericAPIView, mixins.CreateModelMixin
):
    serializer_class = TicketSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    def post(self, request: Request, *args, **kwargs):
        ticket_id = request.data.get('ticket_id')
        log_table = request.data.get('log_table')

        if not ticket_id or not log_table:
            return Response({"message": "Both 'ticket_id' and 'log_table' are required."}, status=status.HTTP_400_BAD_REQUEST)

        if log_table == 'status_log':
            return self.get_status_logs(ticket_id)
        elif log_table == 'owner_log':
            return self.get_owner_logs(ticket_id)
        elif log_table == 'all':
            return self.get_all_logs(ticket_id)
        else:
            return Response({"message": "Invalid log table"}, status=status.HTTP_400_BAD_REQUEST)

    def get_status_logs(self, ticket_id):
        try:
            status_logs = StatusLog.objects.select_related('ticket_id').filter(ticket_id=ticket_id)
            status_serializer = StatusLogSerializer(status_logs, many=True)
            history_logs = {
                'status_logs': status_serializer.data
            }
            response = {"message": f"Get Ticket ({ticket_id})'s status logs Successfully", "data": history_logs}
            return Response(data=response, status=status.HTTP_200_OK)
        except Ticket.DoesNotExist:
            return Response({"error": "Ticket not found"}, status=status.HTTP_404_NOT_FOUND)

    def get_owner_logs(self, ticket_id):
        try:
            owner_logs = OwnerLog.objects.select_related('ticket_id').filter(ticket_id=ticket_id)
            owner_serializer = OwnerLogSerializer(owner_logs, many=True)
            history_logs = {
                'owner_logs': owner_serializer.data
            }
            response = {"message": f"Get Ticket ({ticket_id})'s owner logs Successfully", "data": history_logs}
            return Response(data=response, status=status.HTTP_200_OK)
        except Ticket.DoesNotExist:
            return Response({"error": "Ticket not found"}, status=status.HTTP_404_NOT_FOUND)

    def get_all_logs(self, ticket_id):
        try:
            status_logs = StatusLog.objects.select_related('ticket_id').filter(ticket_id=ticket_id)
            owner_logs = OwnerLog.objects.select_related('ticket_id').filter(ticket_id=ticket_id)
            
            status_serializer = StatusLogSerializer(status_logs, many=True)
            owner_serializer = OwnerLogSerializer(owner_logs, many=True)
            
            history_logs = {
                'status_logs': status_serializer.data,
                'owner_logs': owner_serializer.data
            }
            
            response = {"message": f"Get Ticket ({ticket_id})'s all logs Successfully", "data": history_logs}
            return Response(data=response, status=status.HTTP_200_OK)
        except Ticket.DoesNotExist:
            return Response({"error": "Ticket not found"}, status=status.HTTP_404_NOT_FOUND)

# class GetTicketView(
#     LoggingMixin, generics.ListCreateAPIView
# ):
#     serializer_class = TicketSerializer
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAdminUser]
#     queryset = Ticket.objects.all()
#     filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
#     filterset_fields = ['id', 'status_id__id']
    
#     ordering_fields = ['id']  # Fields that can be used for ordering
#     ordering = ['id']  # Order by id ascending

    
#     search_fields = ['customer_id__name', 'owner_id__username']

#     def get_queryset(self):
#         return super().get_queryset().select_related(
#             'status_id', 
#             'customer_id', 
#             'customer_id__gender_id', 
#             'customer_id__main_interface_id', 
#             'owner_id'
#         )


class GetTicketView(LoggingMixin, generics.ListCreateAPIView):
    serializer_class = EnhancedTicketSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]
    queryset = Ticket.objects.all()
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['id', 'status_id__id']
    
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending
    
    search_fields = ['customer_id__name', 'owner_id__username']

    def get_queryset(self):
        # Get the latest analysis for each ticket using a subquery
        latest_analysis = TicketAnalysis.objects.filter(
            ticket=OuterRef('pk')
        ).order_by('-created_on')
        
        queryset = super().get_queryset().select_related(
            'status_id', 
            'customer_id', 
            'customer_id__gender_id', 
            'customer_id__main_interface_id', 
            'owner_id'
        )
        
        # Annotate tickets with sentiment data
        queryset = queryset.annotate(
            latest_sentiment=Subquery(latest_analysis.values('sentiment')[:1]),
            latest_summary=Subquery(latest_analysis.values('summary')[:1]),
            latest_analysis_id=Subquery(latest_analysis.values('id')[:1]),
            latest_analysis_date=Subquery(latest_analysis.values('created_on')[:1])
        )
        
        return queryset

# Update the serializer to use annotations instead of fetching related data
class EnhancedTicketSerializerOptimized(EnhancedTicketSerializer):
    def get_latest_analysis(self, obj):
        """Get the latest sentiment analysis from the annotated fields"""
        if hasattr(obj, 'latest_sentiment') and obj.latest_sentiment:
            return {
                'sentiment': obj.latest_sentiment,
                'summary': obj.latest_summary,
                'analysis_id': obj.latest_analysis_id,
                'analyzed_at': obj.latest_analysis_date
            }
        return None

class TicketListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = TicketSerializer
    authentication_classes = []
    permission_classes = []

    queryset = Ticket.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user, owner_id=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Ticket Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class TicketRetrieveUpdateDeleteView(
    # LoggingMixin, TicketLogger, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = TicketSerializer
    authentication_classes = []
    permission_classes = []

    queryset = Ticket.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "Ticket Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Ticket Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class TicketTopicList(generics.ListCreateAPIView):
    queryset = TicketTopic.objects.all()
    serializer_class = TicketTopicSerializer
    # permission_classes = [IsAuthenticated]
    permission_classes = []
    filter_backends = [OrderingFilter]
    ordering_fields = ['id', 'case_type', 'case_topic', 'created_on']  # fields that can be sorted
    ordering = ['id']  # default sorting

    def perform_create(self, serializer):
        serializer.save()

class TicketTopicDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = TicketTopic.objects.all()
    serializer_class = TicketTopicSerializer
    # permission_classes = [IsAuthenticated] 
    permission_classes = [] 

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance_name = str(instance)  # Get the string representation of the instance
        self.perform_destroy(instance)
        return Response(
            {
                "message": f"'{instance_name}' has been deleted successfully",
                "status": "success"
            }, 
            status=status.HTTP_200_OK
        )

class TicketPriorityList(generics.ListCreateAPIView):
    queryset = TicketPriority.objects.all()
    serializer_class = TicketPrioritySerializer
    # permission_classes = [IsAuthenticated]
    permission_classes = []
    filter_backends = [OrderingFilter]
    ordering_fields = ['id', 'name', 'level']
    ordering = ['level']  # Default ordering by priority level

class TicketPriorityDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = TicketPriority.objects.all()
    serializer_class = TicketPrioritySerializer
    # permission_classes = [IsAuthenticated]
    permission_classes = []

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance_name = str(instance)
        self.perform_destroy(instance)
        return Response(
            {
                "message": f"Priority '{instance_name}' has been deleted successfully",
                "status": "success"
            }, 
            status=status.HTTP_200_OK
        )

class TicketPriorityUpdateView(APIView):
    """
    View for updating the priority of a specific ticket.
    """
    authentication_classes = [JWTAuthentication]
    # permission_classes = [IsHigherThanAgentOrTicketOwner]
    permission_classes = [IsAuthenticated, TicketOwnershipPermission]
    # permission_classes = [TicketOwnershipPermission]
    
    def get_object(self, id):
        """
        Get ticket object with id (ticket_id) and check permissions on it
        """
        obj = get_object_or_404(Ticket, id=id)
        # This explicitly checks object permissions
        self.check_object_permissions(self.request, obj)
        return obj

    def get(self, request, ticket_id):
        """
        Get the current priority of a specific ticket
        """
        # Verify ticket exists
        # ticket = get_object_or_404(Ticket, id=ticket_id)
        ticket = self.get_object(id=ticket_id)
        
        # Return the priority details
        priority_serializer = TicketPrioritySerializer(ticket.priority)
        
        return Response({
            'ticket_id': ticket_id,
            'priority': priority_serializer.data
        })
    
    def put(self, request, ticket_id):
        """
        Update the priority of a specific ticket
        """
        # Verify ticket exists
        # ticket = get_object_or_404(Ticket, id=ticket_id)
        ticket = self.get_object(id=ticket_id)
        
        # Get the priority ID from the request
        priority_id = request.data.get('priority_id')
        if not priority_id:
            return Response({
                'error': 'Priority ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Verify the priority exists
        try:
            new_priority = TicketPriority.objects.get(id=priority_id, is_active=True)
        except TicketPriority.DoesNotExist:
            return Response({
                'error': f'Priority with ID {priority_id} not found or is inactive'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Store the old priority for response
        old_priority = ticket.priority
        
        # Update the ticket priority
        ticket.priority = new_priority
        ticket.updated_by = request.user
        ticket.save()
        
        # Serialize the priorities for response
        old_priority_data = TicketPrioritySerializer(old_priority).data
        new_priority_data = TicketPrioritySerializer(new_priority).data
        
        return Response({
            'message': 'Ticket priority updated successfully',
            'ticket_id': ticket_id,
            'old_priority': old_priority_data,
            'new_priority': new_priority_data
        })
    
    def post(self, request):
        """
        Create a new priority level (admin only)
        """
        # # Check if user is admin
        # if not request.user.is_staff and not request.user.is_superuser:
        #     return Response({
        #         'error': 'Only administrators can create new priority levels'
        #     }, status=status.HTTP_403_FORBIDDEN)
        
        # Create a new priority
        serializer = TicketPrioritySerializer(data=request.data)
        if serializer.is_valid():
            priority = serializer.save(created_by=request.user)
            return Response({
                'message': 'Priority level created successfully',
                'priority': serializer.data
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, priority_id):
        """
        Soft delete a priority level (admin only)
        """
        # # Check if user is admin
        # if not request.user.is_staff and not request.user.is_superuser:
        #     return Response({
        #         'error': 'Only administrators can delete priority levels'
        #     }, status=status.HTTP_403_FORBIDDEN)
        
        # Find the priority
        priority = get_object_or_404(TicketPriority, id=priority_id)
        
        # Check if this priority is in use
        if Ticket.objects.filter(priority=priority).exists():
            return Response({
                'error': 'Cannot delete a priority level that is in use by tickets',
                'recommendation': 'You can mark it as inactive instead'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Soft delete
        priority.is_active = False
        priority.save()
        
        return Response({
            'message': f'Priority level "{priority.name}" has been deactivated successfully',
            'status': 'success'
        }, status=status.HTTP_200_OK)

class StatusListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = StatusSerializer
    authentication_classes = []
    permission_classes = []

    queryset = Status.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Status Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class StatusRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = StatusSerializer
    authentication_classes = []
    permission_classes = []

    queryset = Status.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "Status Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Status Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class MessageListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = MessageSerializer
    authentication_classes = []
    permission_classes = []

    queryset = Message.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Message Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class MessageRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = MessageSerializer
    authentication_classes = []
    permission_classes = []

    queryset = Message.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "Message Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Message Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class StatusLogListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = StatusLogSerializer
    authentication_classes = []
    permission_classes = []

    queryset = StatusLog.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "StatusLog Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class StatusLogRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = StatusLogSerializer
    authentication_classes = []
    permission_classes = []

    queryset = StatusLog.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "StatusLog Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "StatusLog Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class OwnerLogListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = OwnerLogSerializer
    authentication_classes = []
    permission_classes = []

    queryset = OwnerLog.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "OwnerLog Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class OwnerLogRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = OwnerLogSerializer
    authentication_classes = []
    permission_classes = []

    queryset = OwnerLog.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "OwnerLog Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "OwnerLog Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)
    
# class AutoAssignTicketView(LoggingMixin, generics.GenericAPIView):
#     """
#     This class is called when there is a ticket is required a human user to handle it

#     Steps
#     - Find an available user
#         - If there is an available user, transfer a ticket to an assigned user, change ticket's status change from "open" to "assigned and return response with messages"
#         - If there is no an available user, return messages and a ticket's status change to "waiting" because a ticket require a Supervisor user's attention to transfer a ticket to a user themselves
#     """
#     serializer_class = TicketSerializer
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]

#     def post(self, request, ticket_id):
#         try:
#             ticket = Ticket.objects.get(pk=ticket_id)
#             company_code = request.data.get('company_code', None)
#             department_code = request.data.get('department_code', None)
            
#             if not (company_code or department_code):
#                 return Response(
#                     {"message": "company_code or department_code is required"}, 
#                     status=status.HTTP_400_BAD_REQUEST
#                 )
            
#             available_user = UserStatusService.get_suitable_user(
#                 ticket_interface_name=ticket.ticket_interface.name,
#                 company_code=company_code,
#                 department_code=department_code
#             )
            
#             if available_user:
#                 # Get assigned status
#                 assigned_status = Status.objects.get(name="assigned")
#                 transfer_ticket_owner_v2(ticket, available_user, request.user)
#                 update_ticket_status(ticket, assigned_status, request.user)

#                 # Send notifications if needed based on interface
#                 if ticket.ticket_interface.name == Interface.InterfaceType.LINE:
#                     # Send notification to the LINE group with mention to the assigned user
#                     send_line_message_to_line_user(
#                         ticket=ticket,
#                         line_profile=available_user.line_user_id,
#                         to_group=True,
#                     )
                    
#                     # Notify the customer that their ticket has been assigned
#                     send_line_message_to_line_user(
#                         ticket=ticket,
#                         line_profile=ticket.customer_id.line_user_id,
#                         to_customer=True
#                     )

#                 return Response({
#                     "message": "Ticket assigned successfully",
#                     "data": {
#                         "ticket_id": ticket.id,
#                         "new_owner": {
#                             "id": available_user.id,
#                             "username": available_user.username,
#                             "name": available_user.name,
#                         },
#                         "status": assigned_status.name,
#                         "interface": ticket.ticket_interface.name
#                     }
#                 })
#             else:
#                 # Get waiting status
#                 waiting_status = Status.objects.get(name="waiting")
                
#                 # Update ticket status only
#                 update_ticket_status(ticket, waiting_status, request.user)
                
#                 # Notify a supervisor in case no agent is available
#                 try:
#                     # Get a random supervisor with matching interface
#                     supervisor = get_a_random_user_from_role(
#                         role_name="Supervisor", 
#                         company_code=company_code,
#                         interface_type=ticket.ticket_interface.name
#                     )
                    
#                     if supervisor and ticket.ticket_interface.name == Interface.InterfaceType.LINE:
#                         # Notify supervisor via LINE
#                         send_line_message_to_line_user(
#                             ticket=ticket,
#                             line_profile=supervisor.line_user_id,
#                             to_group=True,
#                         )
                        
#                         # Notify customer about waiting status
#                         send_line_message_to_line_user(
#                             ticket=ticket,
#                             line_profile=ticket.customer_id.line_user_id,
#                             to_customer=True
#                         )
#                 except Exception as e:
#                     print(f"Error notifying supervisor: {str(e)}")

#                 return Response({
#                     "message": "No available agents. Ticket requires supervisor attention.",
#                     "data": {
#                         "ticket_id": ticket.id,
#                         "status": waiting_status.name,
#                         "interface": ticket.ticket_interface.name
#                     }
#                 }, status=status.HTTP_200_OK)

#         except Ticket.DoesNotExist:
#             return Response(
#                 {"message": "Ticket not found"}, 
#                 status=status.HTTP_404_NOT_FOUND
#             )
        
# class TicketOwnersHistoryView(APIView):
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
    
#     def get(self, request, ticket_id):
#         """
#         Get all owners (past and present) of a specific ticket
        
#         Args:
#             ticket_id: ID of the ticket to fetch owner history for
            
#         Returns:
#             List of all owners and ownership change logs for the ticket
#         """
#         try:
#             # Verify ticket exists
#             ticket = get_object_or_404(Ticket, id=ticket_id)
            
#             # Get all owner logs for the ticket, ordered by creation date
#             owner_logs = OwnerLog.objects.filter(
#                 ticket_id=ticket
#             ).order_by('-created_on')
            
#             # Get current owner
#             current_owner = ticket.owner_id
            
#             # Serialize the owner logs
#             owner_logs_serializer = OwnerLogSerializer(owner_logs, many=True)
            
#             # Get unique owners
#             unique_owners = list(set(log.owner_id for log in owner_logs if log.owner_id is not None))
#             unique_owners_serializer = UserSerializer(unique_owners, many=True)
            
#             return Response({
#                 'ticket_id': ticket_id,
#                 'current_owner': UserSerializer(current_owner).data if current_owner else None,
#                 'owner_history': owner_logs_serializer.data,
#                 'unique_owners': unique_owners_serializer.data,
#                 'total_owner_changes': owner_logs.count(),
#             }, status=status.HTTP_200_OK)
            
#         except Exception as e:
#             return Response({
#                 'error': f'Error fetching ticket owners: {str(e)}'
#             }, status=status.HTTP_400_BAD_REQUEST)

# class TicketOwnersHistoryView(APIView):
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
   
#     def get(self, request, ticket_id):
#         """
#         Get all owners (past and present) of a specific ticket
       
#         Args:
#             ticket_id: ID of the ticket to fetch owner history for
           
#         Returns:
#             List of all owners and ownership change logs for the ticket
#         """
#         try:
#             # Verify ticket exists
#             ticket = get_object_or_404(Ticket, id=ticket_id)
           
#             # Get all owner logs for the ticket, ordered by creation date
#             owner_logs = OwnerLog.objects.filter(
#                 ticket_id=ticket
#             ).order_by('-created_on')
           
#             # Get current owner
#             current_owner = ticket.owner_id
            
#             # Get current owner's role
#             current_owner_roles = []
#             if current_owner:
#                 user_roles = UserRole.objects.filter(user_id=current_owner)
#                 current_owner_roles = [{'id': role.role_id.role_id, 'name': role.role_id.name} for role in user_roles]
           
#             # Serialize the owner logs
#             owner_logs_serializer = OwnerLogSerializer(owner_logs, many=True)
           
#             # Get unique owners
#             unique_owners = list(set(log.owner_id for log in owner_logs if log.owner_id is not None))
            
#             # Get roles for each unique owner
#             unique_owners_with_roles = []
#             for owner in unique_owners:
#                 user_roles = UserRole.objects.filter(user_id=owner)

#                 # TODO - Delete this
#                 print(f"TicketOwnersHistoryView's user_roles - {user_roles}")

#                 roles = [{'id': role.role_id.role_id, 'name': role.role_id.name} for role in user_roles]
                
#                 owner_data = UserSerializer(owner).data
#                 owner_data['roles'] = roles
#                 unique_owners_with_roles.append(owner_data)
            
#             # Add role information to each entry in owner_history
#             owner_history_with_roles = []
#             for log_data in owner_logs_serializer.data:
#                 if log_data['owner'] and log_data['owner']['id']:
#                     owner_id = log_data['owner']['id']
#                     user_roles = UserRole.objects.filter(user_id=owner_id)
#                     roles = [{'id': role.role_id.role_id, 'name': role.role_id.name} for role in user_roles]
#                     log_data['owner_roles'] = roles
#                 else:
#                     log_data['owner_roles'] = []
#                 owner_history_with_roles.append(log_data)

#             # Create or augment current_owner data with roles
#             current_owner_data = None
#             if current_owner:
#                 current_owner_data = UserSerializer(current_owner).data
#                 current_owner_data['roles'] = current_owner_roles

#             return Response({
#                 'ticket_id': ticket_id,
#                 'current_owner': current_owner_data,
#                 'owner_history': owner_history_with_roles,
#                 'unique_owners': unique_owners_with_roles,
#                 'total_owner_changes': owner_logs.count(),
#             }, status=status.HTTP_200_OK)
           
#         except Exception as e:
#             return Response({
#                 'error': f'Error fetching ticket owners: {str(e)}'
#             }, status=status.HTTP_400_BAD_REQUEST)


class TicketOwnersHistoryView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
   
    def get(self, request, ticket_id):
        """
        Get all owners (past and present) of a specific ticket
       
        Args:
            ticket_id: ID of the ticket to fetch owner history for
           
        Returns:
            List of all owners and ownership change logs for the ticket
        """
        try:
            # Verify ticket exists
            ticket = get_object_or_404(Ticket, id=ticket_id)
           
            # Get all owner logs for the ticket, ordered by creation date
            owner_logs = OwnerLog.objects.filter(
                ticket_id=ticket
            ).order_by('-created_on')

            # Ticket's status
            ticket_status = ticket.status_id.name if ticket.status_id else None
           
            # Get current owner
            current_owner = ticket.owner_id
            
            # Get current owner's role
            current_owner_roles = []
            if current_owner:
                user_roles = UserRole.objects.filter(user_id=current_owner)
                current_owner_roles = [{'id': role.role_id.role_id, 'name': role.role_id.name} for role in user_roles]
           
            # Serialize the owner logs
            # owner_logs_serializer = OwnerLogSerializer(owner_logs, many=True)
            owner_logs_serializer = OwnerLogBasicSerializer(owner_logs, many=True)
           
            # Get unique owners
            unique_owners = list(set(log.owner_id for log in owner_logs if log.owner_id is not None))
            
            # Get roles for each unique owner
            unique_owners_with_roles = []
            for owner in unique_owners:
                user_roles = UserRole.objects.filter(user_id=owner)

                # TODO - Delete this
                print(f"TicketOwnersHistoryView's user_roles - {user_roles}")

                roles = [{'id': role.role_id.role_id, 'name': role.role_id.name} for role in user_roles]
                
                # owner_data = UserSerializer(owner).data
                owner_data = UserBasicSerializer(owner).data
                owner_data['roles'] = roles
                unique_owners_with_roles.append(owner_data)
            
            # Add role information to each entry in owner_history
            owner_history_with_roles = []
            for log_data in owner_logs_serializer.data:
                if log_data['owner'] and log_data['owner']['id']:
                    owner_id = log_data['owner']['id']
                    user_roles = UserRole.objects.filter(user_id=owner_id)
                    roles = [{'id': role.role_id.role_id, 'name': role.role_id.name} for role in user_roles]
                    log_data['owner_roles'] = roles
                else:
                    log_data['owner_roles'] = []
                owner_history_with_roles.append(log_data)

            # Create or augment current_owner data with roles
            current_owner_data = None
            if current_owner:
                # current_owner_data = UserSerializer(current_owner).data
                current_owner_data = UserBasicSerializer(current_owner).data
                current_owner_data['roles'] = current_owner_roles

            return Response({
                'ticket_id': ticket_id,
                'ticket_status': ticket_status,
                'current_owner': current_owner_data,
                'owner_history': owner_history_with_roles,
                'unique_owners': unique_owners_with_roles,
                'total_owner_changes': owner_logs.count(),
            }, status=status.HTTP_200_OK)
           
        except Exception as e:
            return Response({
                'error': f'Error fetching ticket owners: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)


        
class TicketMessagesView(APIView):
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]
    authentication_classes = []
    permission_classes = []
    
    def get(self, request, ticket_id):
        """
        Get all messages for a specific ticket
        
        Args:
            ticket_id: ID of the ticket to fetch messages for
            
        Returns:
            List of all messages associated with the ticket
        """
        try:
            # Verify ticket exists
            ticket = get_object_or_404(Ticket, id=ticket_id)
            
            # # Check if user has permission to view this ticket
            # if (ticket.owner_id != request.user and 
            #     ticket.customer_id.created_by != request.user and 
            #     not request.user.is_superuser):
            #     return Response({
            #         'error': 'You do not have permission to view these messages'
            #     }, status=status.HTTP_403_FORBIDDEN)
            
            # Get all messages for the ticket, ordered by creation date
            messages = Message.objects.filter(
                ticket_id=ticket
            ).order_by('created_on')
            
            # Serialize the messages
            serializer = MessageSerializer(messages, many=True)

            # TODO - Delete this
            print(f"TicketMessagesView's ticket - {ticket}")
            print(f"TicketMessagesView's message count - {len(messages)}")

            
            # Get message statistics
            total_messages = messages.count()
            user_message_count = messages.filter(is_self=True).count()
            customer_message_count = messages.filter(is_self=False).count()
            
            return Response({
                'ticket_id': ticket_id,
                'customer_id': ticket.customer_id.customer_id if ticket.customer_id else None,
                'owner_id': ticket.owner_id.employee_id if ticket.owner_id else None,
                'messages': serializer.data,
                'statistics': {
                    'total_messages': total_messages,
                    'user_messages': user_message_count,
                    'customer_messages': customer_message_count
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching ticket messages: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
class TicketSummariesView(APIView):
    """
    View for managing summaries for a specific ticket.
    Allows listing, creating, updating, and deleting summaries.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, TicketOwnershipPermission]
    # permission_classes = [IsAuthenticated]
    
    def get(self, request, ticket_id):
        """
        Get all summaries for a specific ticket
        """
        # Verify ticket exists
        ticket = get_object_or_404(Ticket, id=ticket_id)
        
        # Return the summaries as a JSON response
        return Response({
            'ticket_id': ticket_id,
            'summaries': ticket.summaries
        })
    
    def post(self, request, ticket_id):
        """
        Add a new summary to a specific ticket
        """
        # Verify ticket exists
        ticket = get_object_or_404(Ticket, id=ticket_id)
        
        # Get the summary from the request
        summary = request.data.get('summary')
        if not summary:
            return Response({
                'error': 'Summary content is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Create a timestamp for the summary
        summary_entry = {
            'id': len(ticket.summaries) + 1,
            'content': summary,
            'created_by': request.user.username,
            'created_on': timezone.now().isoformat()
        }
        
        # Add the summary to the ticket
        if ticket.summaries is None:
            ticket.summaries = []
        ticket.summaries.append(summary_entry)
        ticket.save()
        
        return Response({
            'message': 'Summary added successfully',
            'summary': summary_entry
        }, status=status.HTTP_201_CREATED)
    
    def put(self, request, ticket_id, summary_id=None):
        """
        Update a specific summary for a ticket
        """
        # Verify ticket exists
        ticket = get_object_or_404(Ticket, id=ticket_id)
        
        if summary_id is None:
            return Response({
                'error': 'Summary ID is required for update'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the updated summary from the request
        new_content = request.data.get('summary')
        if not new_content:
            return Response({
                'error': 'Summary content is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Find the summary with the given ID
        summary_id = int(summary_id)
        found = False
        for i, summary in enumerate(ticket.summaries):
            if summary.get('id') == summary_id:
                # Update the summary content and add updated timestamp
                ticket.summaries[i]['content'] = new_content
                ticket.summaries[i]['updated_by'] = request.user.username
                ticket.summaries[i]['updated_on'] = timezone.now().isoformat()
                found = True
                updated_summary = ticket.summaries[i]
                break
        
        if not found:
            return Response({
                'error': f'Summary with ID {summary_id} not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Save the ticket with updated summaries
        ticket.save()
        
        return Response({
            'message': 'Summary updated successfully',
            'summary': updated_summary
        })
    
    def delete(self, request, ticket_id, summary_id=None):
        """
        Delete a specific summary from a ticket
        """
        # Verify ticket exists
        ticket = get_object_or_404(Ticket, id=ticket_id)
        
        if summary_id is None:
            return Response({
                'error': 'Summary ID is required for deletion'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Find the summary with the given ID
        summary_id = int(summary_id)
        found = False
        for i, summary in enumerate(ticket.summaries):
            if summary.get('id') == summary_id:
                # Remove the summary
                deleted_summary = ticket.summaries.pop(i)
                found = True
                break
        
        if not found:
            return Response({
                'error': f'Summary with ID {summary_id} not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Save the ticket with updated summaries
        ticket.save()
        
        return Response({
            'message': f'Summary with ID {summary_id} deleted successfully',
            'deleted_summary': deleted_summary
        })




























# class ChatMessageListView(APIView):
#     permission_classes = [IsAuthenticated]

#     def get(self, request, ticket_id):
#         """Get chat history for a ticket"""
#         ticket = get_object_or_404(Ticket, id=ticket_id)
        
#         # Check if user has permission to view this ticket
#         if not (request.user.is_superuser or request.user == ticket.owner_id):
#             return Response(
#                 {"error": "You don't have permission to view this ticket's messages"},
#                 status=status.HTTP_403_FORBIDDEN
#             )

#         # Get messages with optional pagination
#         page = int(request.query_params.get('page', 1))
#         page_size = int(request.query_params.get('page_size', 50))
        
#         messages = Message.objects.filter(ticket_id=ticket)\
#             .order_by('-created_on')\
#             .select_related('created_by')[(page-1)*page_size:page*page_size]

#         serializer = ChatMessageSerializer(messages, many=True)
        
#         return Response({
#             'ticket_id': ticket_id,
#             'messages': serializer.data,
#             'has_more': Message.objects.filter(ticket_id=ticket).count() > page*page_size
#         })

# class SendMessageView(APIView):
#     permission_classes = [IsAuthenticated]

#     def post(self, request, ticket_id):
#         """Send a new message in the ticket chat"""
#         ticket = get_object_or_404(Ticket, id=ticket_id)
        
#         # Check permissions
#         if not (request.user.is_superuser or request.user == ticket.owner_id):
#             return Response(
#                 {"error": "You don't have permission to send messages in this ticket"},
#                 status=status.HTTP_403_FORBIDDEN
#             )

#         message_text = request.data.get('message')
#         if not message_text:
#             return Response(
#                 {"error": "Message content is required"},
#                 status=status.HTTP_400_BAD_REQUEST
#             )

#         try:
#             # Create message in database
#             message = Message.objects.create(
#                 ticket_id=ticket,
#                 message=message_text,
#                 user_name=request.user.name,
#                 is_self=True,
#                 created_by=request.user
#             )

#             # Broadcast to WebSocket
#             channel_layer = get_channel_layer()
#             async_to_sync(channel_layer.group_send)(
#                 f'chat_{ticket_id}',
#                 {
#                     'type': 'chat_message',
#                     'message': message_text,
#                     'user_id': request.user.id,
#                     'message_type': 'web',
#                     'timestamp': datetime.now().isoformat()
#                 }
#             )

#             # Send to LINE if customer has LINE user ID
#             if ticket.customer_id and ticket.customer_id.line_user_id:
#                 try:
#                     # Initialize LINE API with proper configuration
#                     line_config = Configuration(
#                         access_token=os.environ["LINE_ACCESS_TOKEN"]
#                     )
                    
#                     with MessagingApi(line_config) as api:
#                         # Format message for LINE
#                         line_message = f"{request.user.name}:\n{message_text}"
                        
#                         # Send to LINE
#                         api.push_message(
#                             to=ticket.customer_id.line_user_id.line_user_id,
#                             messages=[TextMessage(text=line_message)]
#                         )
#                 except Exception as e:
#                     # Log LINE sending error but don't fail the request
#                     print(f"Error sending LINE message: {str(e)}")
#                     return Response({
#                         "data": serializer.data,
#                         "warning": "Message saved but failed to send to LINE"
#                     })

#             serializer = ChatMessageSerializer(message)
#             return Response(serializer.data)

#         except Exception as e:
#             return Response(
#                 {"error": f"Error sending message: {str(e)}"},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )
        

# class TicketAnalysisView(APIView):
#     """
#     API view for handling all operations related to TicketAnalysis instances:
#     - GET: Retrieve all TicketAnalysis for a specific ticket
#     - POST: Create a new TicketAnalysis from API response data
#     - PUT: Update an existing TicketAnalysis
#     - DELETE: Delete an TicketAnalysis
#     """
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated, TicketOwnershipPermission]
    
#     def has_permission(self, request, view, ticket):
#         # Check permissions before processing the request
#         try:
#             return TicketOwnershipPermission().has_object_permission(request, self, ticket)
#         except Ticket.DoesNotExist:
#             return False

#     def get(self, request, ticket_id):
#         """List all analyses for a specific ticket"""
#         ticket = get_object_or_404(Ticket, id=ticket_id)
#         analyses = TicketAnalysis.objects.filter(ticket=ticket).prefetch_related('highlights').order_by('-created_on')
#         serializer = TicketAnalysisSerializer(analyses, many=True)
#         return Response(serializer.data)
    
#     def post(self, request, ticket_id):
#         """Create a new analysis from API response data"""
#         ticket = get_object_or_404(Ticket, id=ticket_id)
        
#         try:
#             # Check: a user's permissions
#             if not self.has_permission(request, self, ticket):
#                 return Response(
#                     {"message": "You don't have permission to create this ticket's analysis."},
#                     status=status.HTTP_403_FORBIDDEN
#                 )
            
#             api_data = request.data
#             output_data = api_data.get('output', {})
#             metadata = api_data.get('metadata', {})
            
#             # Create the TicketAnalysis instance
#             analysis = TicketAnalysis.objects.create(
#                 ticket=ticket,
#                 sentiment=output_data.get('sentiment', ''),
#                 summary=output_data.get('summary', ''),
#                 total_cost=output_data.get('usage', {}).get('total_cost', 0),
#                 total_tokens=output_data.get('usage', {}).get('total_tokens', 0),
#                 prompt_tokens=output_data.get('usage', {}).get('prompt_tokens', 0),
#                 completion_tokens=output_data.get('usage', {}).get('completion_tokens', 0),
#                 successful_requests=output_data.get('usage', {}).get('successful_requests', 0),
#                 run_id=metadata.get('run_id', ''),
#                 is_faq=output_data.get('checkbox', {}).get('faq', {}).get('is_checked', False),
#                 is_recommendation=output_data.get('checkbox', {}).get('recommendation', {}).get('is_checked', False),
#                 is_renewal=output_data.get('checkbox', {}).get('renewal', {}).get('is_checked', False),
#                 is_claim=output_data.get('checkbox', {}).get('claim', {}).get('is_checked', False),
#                 is_complain=output_data.get('checkbox', {}).get('complain', {}).get('is_checked', False),
#                 is_insurance_policy=output_data.get('checkbox', {}).get('insurance_policy', {}).get('is_checked', False),
#                 created_by=request.user,
#                 updated_by=request.user
#             )
            
#             # Create highlights
#             highlights = output_data.get('highlights', [])
#             for index, highlight in enumerate(highlights):
#                 AnalysisHighlight.objects.create(
#                     analysis=analysis,
#                     sentence=highlight.get('sentence', ''),
#                     order=index
#                 )
            
#             # Return the serialized data
#             serializer = TicketAnalysisSerializer(analysis)
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
            
#         except Exception as e:
#             return Response(
#                 {'error': f'Failed to create analysis: {str(e)}'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
    
#     def put(self, request, ticket_id, analysis_id):
#         """Update an existing analysis"""
#         ticket = get_object_or_404(Ticket, id=ticket_id)
#         analysis = get_object_or_404(TicketAnalysis, id=analysis_id, ticket=ticket)
        
#         try:
#             # Check: a user's permissions
#             if not self.has_permission(request, self, ticket):
#                 return Response(
#                     {"message": "You don't have permission to update this ticket's analysis."},
#                     status=status.HTTP_403_FORBIDDEN
#                 )
            
#             # Update basic fields
#             analysis.sentiment = request.data.get('sentiment', analysis.sentiment)
#             analysis.summary = request.data.get('summary', analysis.summary)
            
#             # Update checkbox fields
#             checkboxes = request.data.get('checkboxes', {})
#             if checkboxes:
#                 analysis.is_faq = checkboxes.get('is_faq', analysis.is_faq)
#                 analysis.is_recommendation = checkboxes.get('is_recommendation', analysis.is_recommendation)
#                 analysis.is_renewal = checkboxes.get('is_renewal', analysis.is_renewal)
#                 analysis.is_claim = checkboxes.get('is_claim', analysis.is_claim)
#                 analysis.is_complain = checkboxes.get('is_complain', analysis.is_complain)
#                 analysis.is_insurance_policy = checkboxes.get('is_insurance_policy', analysis.is_insurance_policy)
            
#             # Update the updated_by field
#             analysis.updated_by = request.user

#             # Save the updated analysis
#             analysis.save()
            
#             # Handle highlights updates if provided
#             highlights_data = request.data.get('highlights', None)
#             if highlights_data is not None:
#                 # Delete existing highlights if new ones are provided
#                 analysis.highlights.all().delete()
                
#                 # Create new highlights
#                 for index, highlight in enumerate(highlights_data):
#                     AnalysisHighlight.objects.create(
#                         analysis=analysis,
#                         sentence=highlight.get('sentence', ''),
#                         order=index
#                     )
            
#             # Return the updated data
#             serializer = TicketAnalysisSerializer(analysis)
#             return Response(serializer.data)
            
#         except Exception as e:
#             return Response(
#                 {'error': f'Failed to update analysis: {str(e)}'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
    
#     def delete(self, request, ticket_id, analysis_id):
#         """Delete an analysis"""
#         ticket = get_object_or_404(Ticket, id=ticket_id)
#         analysis = get_object_or_404(TicketAnalysis, id=analysis_id, ticket=ticket)
        
#         try:
#             # Check: a user's permissions
#             if not self.has_permission(request, self, ticket):
#                 return Response(
#                     {"message": "You don't have permission to delete this ticket's analysis."},
#                     status=status.HTTP_403_FORBIDDEN
#                 )
            
#             # This will also delete associated highlights due to the CASCADE relationship
#             analysis.delete()
#             return Response(
#                 {'message': f'Analysis {analysis_id} deleted successfully'},
#                 status=status.HTTP_200_OK
#             )
#         except Exception as e:
#             return Response(
#                 {'error': f'Failed to delete analysis: {str(e)}'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )

class TicketAnalysisView(APIView):
    """
    API view for handling all operations related to TicketAnalysis instances:
    - GET: Retrieve all TicketAnalysis for a specific ticket
    - POST: Create a new TicketAnalysis from API response data
    - PUT: Update an existing TicketAnalysis
    - DELETE: Delete an TicketAnalysis
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, TicketOwnershipPermission]
    
    def has_permission(self, request, view, ticket):
        # Check permissions before processing the request
        try:
            return TicketOwnershipPermission().has_object_permission(request, self, ticket)
        except Ticket.DoesNotExist:
            return False

    def get(self, request, ticket_id):
        """List all analyses for a specific ticket"""
        ticket = get_object_or_404(Ticket, id=ticket_id)
        analyses = TicketAnalysis.objects.filter(ticket=ticket).prefetch_related(
            'highlights', 'keywords'
        ).order_by('-created_on')
        serializer = TicketAnalysisSerializer(analyses, many=True)
        return Response(serializer.data)
    
    def post(self, request, ticket_id):
        """Create a new analysis from API response data"""
        ticket = get_object_or_404(Ticket, id=ticket_id)
        
        try:
            # Check: a user's permissions
            if not self.has_permission(request, self, ticket):
                return Response(
                    {"message": "You don't have permission to create this ticket's analysis."},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            api_data = request.data
            output_data = api_data.get('output', {})
            metadata = api_data.get('metadata', {})
            
            # Get action from request or default to 'api_call'
            action = api_data.get('action', 'api_call')
            
            # Create the TicketAnalysis instance
            analysis = TicketAnalysis.objects.create(
                ticket=ticket,
                sentiment=output_data.get('sentiment', ''),
                summary=output_data.get('summary', {}),  # Store as JSON
                total_cost=output_data.get('usage', {}).get('total_cost', 0),
                total_tokens=output_data.get('usage', {}).get('total_tokens', 0),
                prompt_tokens=output_data.get('usage', {}).get('prompt_tokens', 0),
                completion_tokens=output_data.get('usage', {}).get('completion_tokens', 0),
                successful_requests=output_data.get('usage', {}).get('successful_requests', 0),
                run_id=metadata.get('run_id', ''),
                action=action,
                created_by=request.user,
                updated_by=request.user
            )
            
            # Handle checkboxes if they exist (backwards compatibility)
            checkbox_data = output_data.get('checkbox', {})
            if checkbox_data:
                analysis.is_faq = checkbox_data.get('faq', {}).get('is_checked', False)
                analysis.is_recommendation = checkbox_data.get('recommendation', {}).get('is_checked', False)
                analysis.is_renewal = checkbox_data.get('renewal', {}).get('is_checked', False)
                analysis.is_claim = checkbox_data.get('claim', {}).get('is_checked', False)
                analysis.is_complain = checkbox_data.get('complain', {}).get('is_checked', False)
                analysis.is_insurance_policy = checkbox_data.get('insurance_policy', {}).get('is_checked', False)
                analysis.save()
            
            # Create highlights if they exist (backwards compatibility)
            highlights = output_data.get('highlights', {})
            if isinstance(highlights, list):  # Old format compatibility
                for index, highlight in enumerate(highlights):
                    AnalysisHighlight.objects.create(
                        analysis=analysis,
                        sentence=highlight.get('sentence', ''),
                        order=index
                    )
            
            # Create keywords
            keywords_data = output_data.get('keywords', {})
            if keywords_data:
                # Process customer keywords
                customer_keywords = keywords_data.get('customer', [])
                for index, keyword in enumerate(customer_keywords):
                    AnalysisKeyword.objects.create(
                        analysis=analysis,
                        keyword_type='customer',
                        keyword=keyword,
                        order=index
                    )
                
                # Process user keywords
                user_keywords = keywords_data.get('user', [])
                for index, keyword in enumerate(user_keywords):
                    AnalysisKeyword.objects.create(
                        analysis=analysis,
                        keyword_type='user',
                        keyword=keyword,
                        order=index
                    )
            
            # Return the serialized data
            serializer = TicketAnalysisSerializer(analysis)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to create analysis: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def put(self, request, ticket_id, analysis_id):
        """Update an existing analysis"""
        ticket = get_object_or_404(Ticket, id=ticket_id)
        analysis = get_object_or_404(TicketAnalysis, id=analysis_id, ticket=ticket)
        
        try:
            # Check: a user's permissions
            if not self.has_permission(request, self, ticket):
                return Response(
                    {"message": "You don't have permission to update this ticket's analysis."},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # Update basic fields
            analysis.sentiment = request.data.get('sentiment', analysis.sentiment)
            
            # Update summary (now as JSON)
            summary_data = request.data.get('summary')
            if summary_data is not None:
                analysis.summary = summary_data
            
            # Update action if provided
            if 'action' in request.data:
                analysis.action = request.data['action']
            
            # Update checkbox fields
            checkboxes = request.data.get('checkboxes', {})
            if checkboxes:
                analysis.is_faq = checkboxes.get('is_faq', analysis.is_faq)
                analysis.is_recommendation = checkboxes.get('is_recommendation', analysis.is_recommendation)
                analysis.is_renewal = checkboxes.get('is_renewal', analysis.is_renewal)
                analysis.is_claim = checkboxes.get('is_claim', analysis.is_claim)
                analysis.is_complain = checkboxes.get('is_complain', analysis.is_complain)
                analysis.is_insurance_policy = checkboxes.get('is_insurance_policy', analysis.is_insurance_policy)
            
            # Update the updated_by field
            analysis.updated_by = request.user

            # Save the updated analysis
            analysis.save()
            
            # Handle highlights updates if provided
            highlights_data = request.data.get('highlights', None)
            if highlights_data is not None and isinstance(highlights_data, list):
                # Delete existing highlights if new ones are provided
                analysis.highlights.all().delete()
                
                # Create new highlights
                for index, highlight in enumerate(highlights_data):
                    AnalysisHighlight.objects.create(
                        analysis=analysis,
                        sentence=highlight.get('sentence', ''),
                        order=index
                    )
            
            # Handle keywords updates if provided
            keywords_data = request.data.get('keywords', None)
            if keywords_data is not None:
                # Delete existing keywords
                analysis.keywords.all().delete()
                
                # Process customer keywords
                customer_keywords = keywords_data.get('customer', [])
                for index, keyword in enumerate(customer_keywords):
                    AnalysisKeyword.objects.create(
                        analysis=analysis,
                        keyword_type='customer',
                        keyword=keyword,
                        order=index
                    )
                
                # Process user keywords
                user_keywords = keywords_data.get('user', [])
                for index, keyword in enumerate(user_keywords):
                    AnalysisKeyword.objects.create(
                        analysis=analysis,
                        keyword_type='user',
                        keyword=keyword,
                        order=index
                    )
            
            # Return the updated data
            serializer = TicketAnalysisSerializer(analysis)
            return Response(serializer.data)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to update analysis: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def delete(self, request, ticket_id, analysis_id):
        """Delete an analysis"""
        ticket = get_object_or_404(Ticket, id=ticket_id)
        analysis = get_object_or_404(TicketAnalysis, id=analysis_id, ticket=ticket)
        
        try:
            # Check: a user's permissions
            if not self.has_permission(request, self, ticket):
                return Response(
                    {"message": "You don't have permission to delete this ticket's analysis."},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            # This will also delete associated highlights and keywords due to CASCADE
            analysis.delete()
            return Response(
                {'message': f'Analysis {analysis_id} deleted successfully'},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {'error': f'Failed to delete analysis: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )


# # Single analysis view for direct operations on a specific analysis
# class SingleAnalysisView(APIView):
#     """
#     API view for operations on a single TicketAnalysis instance.
#     Used when accessing the analysis directly by its ID without the ticket context.
#     """
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
    
#     def get(self, request, analysis_id):
#         """Retrieve a specific analysis by ID"""
#         analysis = get_object_or_404(TicketAnalysis, id=analysis_id)
#         serializer = TicketAnalysisSerializer(analysis)
#         return Response(serializer.data)
    
#     def put(self, request, analysis_id):
#         """Update a specific analysis by ID"""
#         analysis = get_object_or_404(TicketAnalysis, id=analysis_id)
        
#         try:
#             # Update basic fields
#             analysis.sentiment = request.data.get('sentiment', analysis.sentiment)
#             analysis.summary = request.data.get('summary', analysis.summary)
            
#             # Update checkbox fields
#             checkboxes = request.data.get('checkboxes', {})
#             if checkboxes:
#                 analysis.is_faq = checkboxes.get('is_faq', analysis.is_faq)
#                 analysis.is_recommendation = checkboxes.get('is_recommendation', analysis.is_recommendation)
#                 analysis.is_renewal = checkboxes.get('is_renewal', analysis.is_renewal)
#                 analysis.is_claim = checkboxes.get('is_claim', analysis.is_claim)
#                 analysis.is_complain = checkboxes.get('is_complain', analysis.is_complain)
#                 analysis.is_insurance_policy = checkboxes.get('is_insurance_policy', analysis.is_insurance_policy)
            
#             # Save the updated analysis
#             analysis.save()
            
#             # Handle highlights updates if provided
#             highlights_data = request.data.get('highlights', None)
#             if highlights_data is not None:
#                 # Delete existing highlights if new ones are provided
#                 analysis.highlights.all().delete()
                
#                 # Create new highlights
#                 for index, highlight in enumerate(highlights_data):
#                     AnalysisHighlight.objects.create(
#                         analysis=analysis,
#                         sentence=highlight.get('sentence', ''),
#                         order=index
#                     )
            
#             # Return the updated data
#             serializer = TicketAnalysisSerializer(analysis)
#             return Response(serializer.data)
            
#         except Exception as e:
#             return Response(
#                 {'error': f'Failed to update analysis: {str(e)}'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
    
#     def delete(self, request, analysis_id):
#         """Delete a specific analysis by ID"""
#         analysis = get_object_or_404(TicketAnalysis, id=analysis_id)
        
#         try:
#             analysis.delete()
#             return Response(
#                 {'message': f'Analysis {analysis_id} deleted successfully'},
#                 status=status.HTTP_204_NO_CONTENT
#             )
#         except Exception as e:
#             return Response(
#                 {'error': f'Failed to delete analysis: {str(e)}'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )

# class SingleAnalysisView(APIView):
#     """
#     API view for operations on a single TicketAnalysis instance.
#     Used when accessing the analysis directly by its ID without the ticket context.
#     """
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
    
#     def get(self, request, analysis_id=None):
#         """
#         Retrieve a specific analysis by ID, or all analyses sorted by created_on if no ID is provided
#         """
#         if analysis_id:
#             analysis = get_object_or_404(TicketAnalysis, id=analysis_id)
#             serializer = TicketAnalysisSerializer(analysis)
#             return Response(serializer.data)
#         else:
#             # If no analysis_id provided, return all analyses sorted by created_on
#             analyses = TicketAnalysis.objects.all().prefetch_related('highlights').order_by('-created_on')
#             serializer = TicketAnalysisSerializer(analyses, many=True)
#             return Response(serializer.data)
    
#     def put(self, request, analysis_id):
#         """Update a specific analysis by ID"""
#         analysis = get_object_or_404(TicketAnalysis, id=analysis_id)
        
#         try:
#             # Update basic fields
#             analysis.sentiment = request.data.get('sentiment', analysis.sentiment)
#             analysis.summary = request.data.get('summary', analysis.summary)
            
#             # Update checkbox fields
#             checkboxes = request.data.get('checkboxes', {})
#             if checkboxes:
#                 analysis.is_faq = checkboxes.get('is_faq', analysis.is_faq)
#                 analysis.is_recommendation = checkboxes.get('is_recommendation', analysis.is_recommendation)
#                 analysis.is_renewal = checkboxes.get('is_renewal', analysis.is_renewal)
#                 analysis.is_claim = checkboxes.get('is_claim', analysis.is_claim)
#                 analysis.is_complain = checkboxes.get('is_complain', analysis.is_complain)
#                 analysis.is_insurance_policy = checkboxes.get('is_insurance_policy', analysis.is_insurance_policy)
            
#             # Update the updated_by field
#             analysis.updated_by = request.user
            
#             # Save the updated analysis
#             analysis.save()
            
#             # Handle highlights updates if provided
#             highlights_data = request.data.get('highlights', None)
#             if highlights_data is not None:
#                 # Delete existing highlights if new ones are provided
#                 analysis.highlights.all().delete()
                
#                 # Create new highlights
#                 for index, highlight in enumerate(highlights_data):
#                     AnalysisHighlight.objects.create(
#                         analysis=analysis,
#                         sentence=highlight.get('sentence', ''),
#                         order=index
#                     )
            
#             # Return the updated data
#             serializer = TicketAnalysisSerializer(analysis)
#             return Response(serializer.data)
            
#         except Exception as e:
#             return Response(
#                 {'error': f'Failed to update analysis: {str(e)}'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )
    
#     def delete(self, request, analysis_id):
#         """Delete a specific analysis by ID"""
#         analysis = get_object_or_404(TicketAnalysis, id=analysis_id)
        
#         try:
#             analysis.delete()
#             return Response(
#                 {'message': f'Analysis {analysis_id} deleted successfully'},
#                 status=status.HTTP_204_NO_CONTENT
#             )
#         except Exception as e:
#             return Response(
#                 {'error': f'Failed to delete analysis: {str(e)}'},
#                 status=status.HTTP_400_BAD_REQUEST
#             )

class SingleAnalysisView(APIView):
    """
    API view for operations on a single TicketAnalysis instance.
    Used when accessing the analysis directly by its ID without the ticket context.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, analysis_id=None):
        """
        Retrieve a specific analysis by ID, or all analyses sorted by created_on if no ID is provided
        """
        if analysis_id:
            analysis = get_object_or_404(TicketAnalysis, id=analysis_id)
            serializer = TicketAnalysisSerializer(analysis)
            return Response(serializer.data)
        else:
            # If no analysis_id provided, return all analyses sorted by created_on
            analyses = TicketAnalysis.objects.all().prefetch_related(
                'highlights', 'keywords'
            ).order_by('-created_on')
            serializer = TicketAnalysisSerializer(analyses, many=True)
            return Response(serializer.data)
    
    def put(self, request, analysis_id):
        """Update a specific analysis by ID"""
        analysis = get_object_or_404(TicketAnalysis, id=analysis_id)
        
        try:
            # Update basic fields
            analysis.sentiment = request.data.get('sentiment', analysis.sentiment)
            
            # Update summary (now as JSON)
            summary_data = request.data.get('summary')
            if summary_data is not None:
                analysis.summary = summary_data
            
            # Update action if provided
            if 'action' in request.data:
                analysis.action = request.data['action']
            
            # Update checkbox fields
            checkboxes = request.data.get('checkboxes', {})
            if checkboxes:
                analysis.is_faq = checkboxes.get('is_faq', analysis.is_faq)
                analysis.is_recommendation = checkboxes.get('is_recommendation', analysis.is_recommendation)
                analysis.is_renewal = checkboxes.get('is_renewal', analysis.is_renewal)
                analysis.is_claim = checkboxes.get('is_claim', analysis.is_claim)
                analysis.is_complain = checkboxes.get('is_complain', analysis.is_complain)
                analysis.is_insurance_policy = checkboxes.get('is_insurance_policy', analysis.is_insurance_policy)
            
            # Update the updated_by field
            analysis.updated_by = request.user
            
            # Save the updated analysis
            analysis.save()
            
            # Handle highlights updates if provided
            highlights_data = request.data.get('highlights', None)
            if highlights_data is not None and isinstance(highlights_data, list):
                # Delete existing highlights if new ones are provided
                analysis.highlights.all().delete()
                
                # Create new highlights
                for index, highlight in enumerate(highlights_data):
                    AnalysisHighlight.objects.create(
                        analysis=analysis,
                        sentence=highlight.get('sentence', ''),
                        order=index
                    )
            
            # Handle keywords updates if provided
            keywords_data = request.data.get('keywords', None)
            if keywords_data is not None:
                # Delete existing keywords
                analysis.keywords.all().delete()
                
                # Process customer keywords
                customer_keywords = keywords_data.get('customer', [])
                for index, keyword in enumerate(customer_keywords):
                    AnalysisKeyword.objects.create(
                        analysis=analysis,
                        keyword_type='customer',
                        keyword=keyword,
                        order=index
                    )
                
                # Process user keywords
                user_keywords = keywords_data.get('user', [])
                for index, keyword in enumerate(user_keywords):
                    AnalysisKeyword.objects.create(
                        analysis=analysis,
                        keyword_type='user',
                        keyword=keyword,
                        order=index
                    )
            
            # Return the updated data
            serializer = TicketAnalysisSerializer(analysis)
            return Response(serializer.data)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to update analysis: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def delete(self, request, analysis_id):
        """Delete a specific analysis by ID"""
        analysis = get_object_or_404(TicketAnalysis, id=analysis_id)
        
        try:
            analysis.delete()
            return Response(
                {'message': f'Analysis {analysis_id} deleted successfully'},
                status=status.HTTP_204_NO_CONTENT
            )
        except Exception as e:
            return Response(
                {'error': f'Failed to delete analysis: {str(e)}'},
                status=status.HTTP_400_BAD_REQUEST
            )

# class LatestTicketAnalysisView(APIView):
#     """
#     API view to get the latest analysis for a specific ticket
#     """
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
    
#     def get(self, request, ticket_id):
#         """Get the latest analysis for a specific ticket"""
#         ticket = get_object_or_404(Ticket, id=ticket_id)
        
#         # Get the latest analysis
#         latest_analysis = TicketAnalysis.objects.filter(
#             ticket=ticket
#         ).order_by('-created_on').first()
        
#         if latest_analysis:
#             serializer = TicketAnalysisSerializer(latest_analysis)
#             return Response(serializer.data)
#         else:
#             return Response(
#                 {'message': 'No analysis found for this ticket'},
#                 status=status.HTTP_404_NOT_FOUND
#             )

class LatestTicketAnalysisView(APIView):
    """
    API view to get the latest analysis for a specific ticket
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, ticket_id):
        """Get the latest analysis for a specific ticket"""
        ticket = get_object_or_404(Ticket, id=ticket_id)
        
        # Get the latest analysis
        latest_analysis = TicketAnalysis.objects.filter(
            ticket=ticket
        ).prefetch_related('highlights', 'keywords').order_by('-created_on').first()
        
        if latest_analysis:
            serializer = TicketAnalysisSerializer(latest_analysis)
            return Response(serializer.data)
        else:
            return Response(
                {'message': 'No analysis found for this ticket'},
                status=status.HTTP_404_NOT_FOUND
            )










class TicketTopicsUpdateView(APIView):
    """
    View for updating the topics (case_type, case_topic) associated with a ticket
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, ticket_id):
        """
        Get the current topics associated with a ticket
        
        Returns a list of all topics associated with the ticket
        """
        try:
            ticket = get_object_or_404(Ticket, id=ticket_id)
            
            # # Check if the user has permission to view this ticket
            # if not (request.user.is_superuser or request.user == ticket.owner_id):
            #     return Response(
            #         {"error": "You don't have permission to view this ticket's topics"},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            topics = ticket.topics.all()
            serializer = TicketTopicSerializer(topics, many=True)
            
            return Response({
                'ticket_id': ticket_id,
                'topics': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error retrieving ticket topics: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request, ticket_id):
        """
        Add topics to a ticket
        
        Request body: {"topic_ids": [1, 2, 3]}
        """
        try:
            ticket = get_object_or_404(Ticket, id=ticket_id)
            
            # # Check if the user has permission to update this ticket
            # if not (request.user.is_superuser or request.user == ticket.owner_id):
            #     return Response(
            #         {"error": "You don't have permission to update this ticket's topics"},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            # Get topic IDs from request data
            topic_ids = request.data.get('topic_ids', [])
            if not topic_ids:
                return Response({
                    'error': 'No topic_ids provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Verify all topics exist
            topics = TicketTopic.objects.filter(id__in=topic_ids, is_active=True)
            if len(topics) != len(topic_ids):
                found_ids = set(topics.values_list('id', flat=True))
                missing_ids = set(topic_ids) - found_ids
                return Response({
                    'error': f'The following topics were not found or are inactive: {missing_ids}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Add topics to ticket
            ticket.topics.add(*topics)
            
            # Return updated topics
            updated_topics = ticket.topics.all()
            serializer = TicketTopicSerializer(updated_topics, many=True)
            
            return Response({
                'message': 'Topics added successfully',
                'ticket_id': ticket_id,
                'topics': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error adding topics to ticket: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def put(self, request, ticket_id):
        """
        Replace all topics for a ticket
        
        Request body: {"topic_ids": [1, 2, 3]}
        """
        try:
            ticket = get_object_or_404(Ticket, id=ticket_id)
            
            # # Check if the user has permission to update this ticket
            # if not (request.user.is_superuser or request.user == ticket.owner_id):
            #     return Response(
            #         {"error": "You don't have permission to update this ticket's topics"},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            # Get topic IDs from request data
            topic_ids = request.data.get('topic_ids', [])
            
            # If empty list, clear all topics
            if topic_ids == []:
                ticket.topics.clear()
                return Response({
                    'message': 'All topics removed from ticket',
                    'ticket_id': ticket_id,
                    'topics': []
                }, status=status.HTTP_200_OK)
            
            # Verify all topics exist
            topics = TicketTopic.objects.filter(id__in=topic_ids, is_active=True)
            if len(topics) != len(topic_ids):
                found_ids = set(topics.values_list('id', flat=True))
                missing_ids = set(topic_ids) - found_ids
                return Response({
                    'error': f'The following topics were not found or are inactive: {missing_ids}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Replace all topics
            ticket.topics.clear()
            ticket.topics.add(*topics)
            
            # Return updated topics
            serializer = TicketTopicSerializer(topics, many=True)
            
            return Response({
                'message': 'Topics updated successfully',
                'ticket_id': ticket_id,
                'topics': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error updating ticket topics: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, ticket_id):
        """
        Remove specific topics from a ticket
        
        Request body: {"topic_ids": [1, 2, 3]}
        """
        try:
            ticket = get_object_or_404(Ticket, id=ticket_id)
            
            # # Check if the user has permission to update this ticket
            # if not (request.user.is_superuser or request.user == ticket.owner_id):
            #     return Response(
            #         {"error": "You don't have permission to update this ticket's topics"},
            #         status=status.HTTP_403_FORBIDDEN
            #     )
            
            # Get topic IDs from request data
            topic_ids = request.data.get('topic_ids', [])
            if not topic_ids:
                return Response({
                    'error': 'No topic_ids provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Remove the specified topics
            removed_count = 0
            for topic_id in topic_ids:
                try:
                    topic = TicketTopic.objects.get(id=topic_id)
                    if topic in ticket.topics.all():
                        ticket.topics.remove(topic)
                        removed_count += 1
                except TicketTopic.DoesNotExist:
                    continue
            
            # Return remaining topics
            remaining_topics = ticket.topics.all()
            serializer = TicketTopicSerializer(remaining_topics, many=True)
            
            return Response({
                'message': f'Removed {removed_count} topics from ticket',
                'ticket_id': ticket_id,
                'topics': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error removing topics from ticket: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)











class MessageListView(APIView):
    # serializer_class = WebSocketsMessageSerializer

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    # authentication_classes = []
    # permission_classes = []

    def get(self, request, ticket_id):
        """Get message history for a ticket"""
        ticket = get_object_or_404(Ticket, id=ticket_id)
        messages = Message.objects.filter(ticket_id=ticket).order_by('created_on')
        serializer = WebSocketsMessageSerializer(messages, many=True)
        return Response(serializer.data)

    def post(self, request, ticket_id):
        """Create a new message"""
        ticket = get_object_or_404(Ticket, id=ticket_id)

        # # TODO - Delete this
        # print(f"MessageListView's request's user - {self.request.user}")
        # print(f"MessageListView's request's data - {self.request.data}")
        
        # Get the authenticated user from the request (based on access token)
        user = request.user # User instance
        if user.first_name or user.last_name:
            if user.first_name and user.last_name:
                user_fullname = user.first_name + " " + user.last_name
            elif user.first_name:
                user_fullname = user.first_name
            elif user.last_name:
                user_fullname = user.last_name
        elif user.name:
            user_fullname = user.name
        else:
            user_fullname = "Unknown User"
        request.data["user_name"] = user_fullname

        # TODO - Delete this or Log this
        print(f"WSS's MessageListView's post's user - {user}")
        print(f"WSS's MessageListView's post's data - {request.data}")

        serializer = WebSocketsMessageSerializer(data=request.data)

        if serializer.is_valid():
            message = serializer.save(
                ticket_id=ticket,
                created_by=request.user,
                status=Message.MessageStatus.SENT
            )

            # # Broadcast the message via WebSocket
            # channel_layer = get_channel_layer()
            # async_to_sync(channel_layer.group_send)(
            #     f'chat_{ticket_id}',
            #     {
            #         'type': 'chat_message',
            #         'message': serializer.data
            #     }
            # )

            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class MessageStatusView(APIView):
    # serializer_class = WebSocketsMessageSerializer

    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]

    authentication_classes = []
    permission_classes = []

    def post(self, request, message_id):
        """Update message status"""
        message = get_object_or_404(Message, id=message_id)
        new_status = request.data.get('status')
        
        if new_status not in dict(Message.MessageStatus.choices):
            return Response(
                {'error': 'Invalid status'}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update status and timestamp
        message.status = new_status
        if new_status == Message.MessageStatus.DELIVERED:
            message.delivered_on = timezone.now()
        elif new_status == Message.MessageStatus.READ:
            message.read_on = timezone.now()
        message.save()

        # Broadcast status update
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f'chat_{message.ticket_id_id}',
            {
                'type': 'chat_message',
                'message': {
                    'type': 'status_update',
                    'message_id': message_id,
                    'status': new_status
                },
                'message_type': message.message_type,
            }
        )

        return Response({'status': 'updated'})

# class TicketFilter(filters.FilterSet):
#     """
#     Custom filter set for Ticket model that adds sentiment filtering
#     """
#     sentiment = filters.CharFilter(method='filter_sentiment')
    
#     class Meta:
#         model = Ticket
#         fields = ['id', 'status_id__id', 'status_id__name', 'priority__id', 'priority__name']
    
#     def filter_sentiment(self, queryset, name, value):
#         """
#         Filter tickets based on their latest sentiment analysis.
#         Accepts 'Positive', 'Neutral', 'Negative' as values.
#         """
#         # Validate the sentiment value
#         valid_sentiments = ['Positive', 'Neutral', 'Negative']
#         if value.capitalize() not in valid_sentiments:
#             return queryset
#         # TODO - Delete this
#         print(f"TicketFilter's value - {value}")

#         # Get ticket IDs that have the specified sentiment in their latest analysis
#         latest_analyses = TicketAnalysis.objects.filter(
#             sentiment=value.capitalize()
#         ).order_by('ticket_id', '-created_on').distinct('ticket_id')

#         # TODO - Delete this
#         print(f"TicketFilter's latest_analyses - {latest_analyses}")
        
#         ticket_ids = [analysis.ticket.id for analysis in latest_analyses]
        
#         # Filter the queryset to include only tickets with the matching sentiment
#         return queryset.filter(id__in=ticket_ids)

# class TicketListSlimPaginatedView(generics.ListAPIView):
#     """
#     Optimized endpoint that returns paginated ticket data for ticket listings,
#     significantly reducing payload size and API response time.
#     """
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
    
#     pagination_class = StandardResultsSetPagination
    
#     filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
#     filterset_fields = ['id', 'status_id__id', 'status_id__name', 'priority__id']
#     # filterset_class = TicketFilter
#     search_fields = ['customer_id__name', 'owner_id__username', 'id']
#     ordering_fields = ['id', 'updated_on']
#     # ordering = ['-updated_on']  # Default to most recently updated
#     ordering = ['-id']  # Default to most recently ticket

#     def get_queryset(self):
#         """
#         Get optimized queryset with only the fields needed for the ticket list display
#         """
#         queryset = Ticket.objects.select_related(
#             'status_id',
#             'customer_id',
#             'owner_id',
#             'priority'
#         ).values(
#             'id',
#             'status_id__name',
#             'status_id__id',
#             'priority__name',
#             'priority__id',
#             'customer_id__name',
#             'customer_id__email',
#             'owner_id__name',
#             'owner_id__username',
#             'updated_on',
#             # Add role info for the owner
#             'owner_id__userrole__role_id__name',
#         )
        
#         return queryset

#     def list(self, request, *args, **kwargs):
#         """
#         Override the default list method to include the sentiment data
#         from the latest ticket analysis.
#         """
#         # Get paginated queryset
#         queryset = self.filter_queryset(self.get_queryset())
#         page = self.paginate_queryset(queryset)
        
#         if page is not None:
#             # Get all ticket IDs for efficient bulk lookup
#             ticket_ids = [ticket['id'] for ticket in page]
            
#             # Fetch the latest ticket analysis for each ticket
#             latest_analyses = {}
#             # analyses = TicketAnalysis.objects.filter(
#             #     ticket_id__id__in=ticket_ids
#             # ).order_by('ticket_id', '-analyzed_on')

#             analyses = TicketAnalysis.objects.filter(
#                 # ticket_id__id__in=ticket_ids
#                 ticket__id__in=ticket_ids
#             # ).order_by('ticket_id', '-created_on').distinct('ticket_id')
#             ).order_by('ticket__id', '-created_on').distinct('ticket_id')
            
#             # # TODO - Delete this
#             print(f"TicketListSlimView's analyses - {analyses}")
#             # Group by ticket_id and keep only the latest analysis
#             for analysis in analyses:
#                 ticket_id = analysis.ticket.id
#                 if ticket_id not in latest_analyses:
#                     latest_analyses[ticket_id] = analysis
#             # # TODO - Delete this
#             print(f"TicketListSlimView's latest_analyses - {latest_analyses}")
            
#             # Prepare response format
#             tickets = []
#             for ticket in page:
#                 ticket_id = ticket['id']

#                 # TODO - Delete this
#                 print(f"TicketListSlimView's ticket_id - {ticket_id}")
            
                
#                 # Get sentiment emoji based on the latest analysis
#                 sentiment = None
#                 # sentiment_emoji = '😐'  # Default neutral
#                 if ticket_id in latest_analyses:
#                     sentiment = latest_analyses[ticket_id].sentiment
#                     # if sentiment == 'positive':
#                     #     sentiment_emoji = '😊'
#                     # elif sentiment == 'negative':
#                     #     sentiment_emoji = '😠'
                
#                 tickets.append({
#                     'id': ticket_id,
#                     'status': {
#                         'id': ticket['status_id__id'],
#                         'name': ticket['status_id__name'],
#                     },
#                     'priority': {
#                         'id': ticket['priority__id'],
#                         'name': ticket['priority__name'],
#                     },
#                     'sentiment': sentiment,
#                     'custoemr': {
#                         'name': ticket['owner_id__name'],
#                         'email': ticket['customer_id__email'],
#                     },
#                     'agent': {
#                         'name': ticket['owner_id__name'],
#                         'username': ticket['owner_id__username'],
#                         'role': ticket['owner_id__userrole__role_id__name'],
#                     },
#                     'time_ago': self._get_time_ago(ticket['updated_on']),
#                     'updated_on': ticket['updated_on'].strftime('%d %b %Y %H:%M'),
#                 })
            
#             # Use the paginator's get_paginated_response
#             return self.get_paginated_response(tickets)
        
#         # If pagination is disabled, return all results
#         return Response(tickets)
    
#     def _get_time_ago(self, timestamp):
#         """Helper method to calculate relative time for UI display"""
#         from django.utils import timezone
#         from datetime import timedelta
        
#         now = timezone.now()
#         diff = now - timestamp
        
#         if diff < timedelta(minutes=60):
#             minutes = int(diff.total_seconds() / 60)
#             return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
#         elif diff < timedelta(hours=24):
#             hours = int(diff.total_seconds() / 3600)
#             return f"{hours} hour{'s' if hours != 1 else ''} ago"
#         elif diff < timedelta(days=7):
#             days = diff.days
#             return f"{days} day{'s' if days != 1 else ''} ago"
#         elif diff < timedelta(days=30):
#             weeks = int(diff.days / 7)
#             return f"{weeks} week{'s' if weeks != 1 else ''} ago"
#         else:
#             months = int(diff.days / 30)
#             return f"{months} month{'s' if months != 1 else ''} ago"










# class TicketFilter(filters.FilterSet):
#     """
#     Custom filter set for Ticket model that supports multiple values for each filter
#     """
#     status_id = filters.CharFilter(method='filter_status_ids')
#     priority_id = filters.CharFilter(method='filter_priority_ids')
#     sentiment = filters.CharFilter(method='filter_sentiment')
    
#     class Meta:
#         model = Ticket
#         fields = ['id', 'status_id', 'priority_id']
    
#     def filter_status_ids(self, queryset, name, value):
#         """
#         Filter tickets based on multiple status IDs
#         Format: 'status_id=1,2,3'
#         """
#         if not value:
#             return queryset
            
#         status_ids = value.split(',')
#         if not status_ids:
#             return queryset
            
#         return queryset.filter(status_id__id__in=status_ids)
    
#     def filter_priority_ids(self, queryset, name, value):
#         """
#         Filter tickets based on multiple priority IDs
#         Format: 'priority_id=1,2,3'
#         """
#         if not value:
#             return queryset
            
#         priority_ids = value.split(',')
#         if not priority_ids:
#             return queryset
            
#         return queryset.filter(priority__id__in=priority_ids)
    
#     def filter_sentiment(self, queryset, name, value):
#         """
#         Filter tickets based on multiple sentiment values.
#         Format: 'sentiment=Positive,Neutral,Negative'
#         """
#         if not value:
#             return queryset
            
#         sentiments = [s.capitalize() for s in value.split(',')]
#         valid_sentiments = ['Positive', 'Neutral', 'Negative']
        
#         # Filter out invalid sentiments
#         sentiments = [s for s in sentiments if s in valid_sentiments]
        
#         if not sentiments:
#             return queryset

#         # Get ticket IDs that have any of the specified sentiments in their latest analysis
#         latest_analyses = TicketAnalysis.objects.filter(
#             sentiment__in=sentiments
#         ).order_by('ticket_id', '-created_on').distinct('ticket_id')
        
#         ticket_ids = [analysis.ticket.id for analysis in latest_analyses]
        
#         # Filter the queryset to include only tickets with matching sentiments
#         return queryset.filter(id__in=ticket_ids)

# class TicketListSlimPaginatedView(generics.ListAPIView):
#     """
#     Optimized endpoint that returns paginated ticket data for ticket listings,
#     with support for multiple values per filter.
#     """
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
    
#     pagination_class = StandardResultsSetPagination
    
#     filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
#     filterset_class = TicketFilter  # Use our custom filter class
#     search_fields = ['customer_id__name', 'owner_id__username', 'id']
#     ordering_fields = ['id', 'updated_on']
#     ordering = ['-id']  # Default to most recently created ticket

#     def get_queryset(self):
#         """
#         Get optimized queryset with only the fields needed for the ticket list display
#         """
#         queryset = Ticket.objects.select_related(
#             'status_id',
#             'customer_id',
#             'owner_id',
#             'priority'
#         ).values(
#             'id',
#             'status_id__name',
#             'status_id__id',
#             'priority__name',
#             'priority__id',
#             'customer_id__name',
#             'customer_id__email',
#             'owner_id__name',
#             'owner_id__username',
#             'updated_on',
#             # Add role info for the owner
#             'owner_id__userrole__role_id__name',
#         )
        
#         return queryset

#     def list(self, request, *args, **kwargs):
#         """
#         Override the default list method to include the sentiment data
#         from the latest ticket analysis.
#         """
#         # Get paginated queryset
#         queryset = self.filter_queryset(self.get_queryset())
#         page = self.paginate_queryset(queryset)
        
#         if page is not None:
#             # Get all ticket IDs for efficient bulk lookup
#             ticket_ids = [ticket['id'] for ticket in page]
            
#             # Fetch the latest ticket analysis for each ticket
#             latest_analyses = {}
            
#             analyses = TicketAnalysis.objects.filter(
#                 ticket__id__in=ticket_ids
#             ).order_by('ticket__id', '-created_on').distinct('ticket_id')
            
#             # Group by ticket_id and keep only the latest analysis
#             for analysis in analyses:
#                 ticket_id = analysis.ticket.id
#                 if ticket_id not in latest_analyses:
#                     latest_analyses[ticket_id] = analysis
            
#             # Prepare response format
#             tickets = []
#             for ticket in page:
#                 ticket_id = ticket['id']
                
#                 # Get sentiment based on the latest analysis
#                 sentiment = None
#                 if ticket_id in latest_analyses:
#                     sentiment = latest_analyses[ticket_id].sentiment
                
#                 tickets.append({
#                     'id': ticket_id,
#                     'status': {
#                         'id': ticket['status_id__id'],
#                         'name': ticket['status_id__name'],
#                     },
#                     'priority': {
#                         'id': ticket['priority__id'],
#                         'name': ticket['priority__name'],
#                     },
#                     'sentiment': sentiment,
#                     'customer': {  # Fixed typo from 'custoemr' to 'customer'
#                         'name': ticket['customer_id__name'],  # Fixed from 'owner_id__name' to 'customer_id__name'
#                         'email': ticket['customer_id__email'],
#                     },
#                     'agent': {
#                         'name': ticket['owner_id__name'],
#                         'username': ticket['owner_id__username'],
#                         'role': ticket['owner_id__userrole__role_id__name'],
#                     },
#                     'time_ago': self._get_time_ago(ticket['updated_on']),
#                     'updated_on': ticket['updated_on'].strftime('%d %b %Y %H:%M'),
#                 })
            
#             # Use the paginator's get_paginated_response
#             return self.get_paginated_response(tickets)
        
#         # If pagination is disabled, return all results
#         return Response(tickets)
    
#     def _get_time_ago(self, timestamp):
#         """Helper method to calculate relative time for UI display"""
#         from django.utils import timezone
#         from datetime import timedelta
        
#         now = timezone.now()
#         diff = now - timestamp
        
#         if diff < timedelta(minutes=60):
#             minutes = int(diff.total_seconds() / 60)
#             return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
#         elif diff < timedelta(hours=24):
#             hours = int(diff.total_seconds() / 3600)
#             return f"{hours} hour{'s' if hours != 1 else ''} ago"
#         elif diff < timedelta(days=7):
#             days = diff.days
#             return f"{days} day{'s' if days != 1 else ''} ago"
#         elif diff < timedelta(days=30):
#             weeks = int(diff.days / 7)
#             return f"{weeks} week{'s' if weeks != 1 else ''} ago"
#         else:
#             months = int(diff.days / 30)
#             return f"{months} month{'s' if months != 1 else ''} ago"


class TicketFilter(filters.FilterSet):
    """
    Custom filter set for Ticket model that supports multiple values for each filter
    """
    # status_id = filters.CharFilter(method='filter_status_ids')
    status_name = filters.CharFilter(method='filter_status_names')
    # priority_id = filters.CharFilter(method='filter_priority_ids')
    priority_name = filters.CharFilter(method='filter_priority_names')
    sentiment = filters.CharFilter(method='filter_sentiment')
    my_tickets = filters.BooleanFilter(method='filter_my_tickets')
    
    class Meta:
        model = Ticket
        fields = ['id', 'status_id', 'status_name', 'priority_id', 'priority_name', 'my_tickets']
    
    # def filter_status_ids(self, queryset, name, value):
    #     """
    #     Filter tickets based on multiple status IDs
    #     Format: 'status_id=1,2,3'
    #     """
    #     if not value:
    #         return queryset
            
    #     status_ids = value.split(',')
    #     if not status_ids:
    #         return queryset
            
    #     return queryset.filter(status_id__id__in=status_ids)
    
    def filter_status_names(self, queryset, name, value):
        """
        Filter tickets based on multiple status names
        Format: 'status_name=open,closed'
        """
        if not value:
            return queryset
            
        status_names = [s.strip().lower() for s in value.split(',')]
        if not status_names:
            return queryset
            
        return queryset.filter(status_id__name__in=status_names)
    
    # def filter_priority_ids(self, queryset, name, value):
    #     """
    #     Filter tickets based on multiple priority IDs
    #     Format: 'priority_id=1,2,3'
    #     """
    #     if not value:
    #         return queryset
            
    #     priority_ids = value.split(',')
    #     if not priority_ids:
    #         return queryset
            
    #     return queryset.filter(priority__id__in=priority_ids)
    
    def filter_priority_names(self, queryset, name, value):
        """
        Filter tickets based on multiple priority names
        Format: 'priority_name=low,medium,high'
        """
        if not value:
            return queryset
            
        priority_names = [p.strip().capitalize() for p in value.split(',')]
        if not priority_names:
            return queryset
            
        return queryset.filter(priority__name__in=priority_names)
    
    def filter_sentiment(self, queryset, name, value):
        """
        Filter tickets based on multiple sentiment values.
        Format: 'sentiment=Positive,Neutral,Negative'
        """
        if not value:
            return queryset
            
        sentiments = [s.capitalize() for s in value.split(',')]
        valid_sentiments = ['Positive', 'Neutral', 'Negative']
        
        # Filter out invalid sentiments
        sentiments = [s for s in sentiments if s in valid_sentiments]
        
        if not sentiments:
            return queryset

        # Get ticket IDs that have any of the specified sentiments in their latest analysis
        latest_analyses = TicketAnalysis.objects.filter(
            sentiment__in=sentiments
        ).order_by('ticket_id', '-created_on').distinct('ticket_id')
        
        ticket_ids = [analysis.ticket.id for analysis in latest_analyses]
        
        # Filter the queryset to include only tickets with matching sentiments
        return queryset.filter(id__in=ticket_ids)
    
    def filter_my_tickets(self, queryset, name, value):
        """
        Filter tickets to show only those owned by the current user.
        Uses the user's name to match against ticket owner's name.
        """
        if not value:
            return queryset
            
        # Get the current user from the request
        request = self.request
        if not request or not request.user or not request.user.is_authenticated:
            return queryset
            
        current_user = request.user
        
        # Filter tickets where the owner's name matches the current user's name
        return queryset.filter(owner_id__name=current_user.name)

class TicketListSlimPaginatedView(generics.ListAPIView):
    """
    Optimized endpoint that returns paginated ticket data for ticket listings,
    with support for multiple values per filter.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    pagination_class = StandardResultsSetPagination
    
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = TicketFilter  # Use our custom filter class
    search_fields = ['id', 'customer_id__name', 'owner_id__name', 'owner_id__username', 'status_id__name', 'priority__name']
    ordering_fields = [
        'id', 
        'updated_on',
        'status_id__id',
        'priority__id',
        'customer_id__name',
        'owner_id__name',
        'created_on',
        'latest_sentiment'
    ]
    ordering = ['status_id__id', '-updated_on']  # Default sort by status then updated_on

    def get_queryset(self):
        """
        Get optimized queryset with only the fields needed for the ticket list display
        """
        # Get the latest analysis for each ticket using a subquery
        latest_analysis = TicketAnalysis.objects.filter(
            ticket=OuterRef('pk')
        ).order_by('-created_on')
        
        # Join related tables with select_related and annotate with sentiment
        queryset = Ticket.objects.select_related(
            'status_id',
            'customer_id',
            'owner_id',
            'priority'
        ).annotate(
            latest_sentiment=Subquery(latest_analysis.values('sentiment')[:1])
        ).values(
            'id',
            'status_id__name',
            'status_id__id',
            'priority__name',
            'priority__id',
            'customer_id__name',
            'customer_id__email',
            'owner_id__id',
            'owner_id__name',
            'owner_id__username',
            'platform_identity__platform',
            'platform_identity__channel_name',
            'updated_on',
            'created_on',  # Add created_on for sorting purpose
            'latest_sentiment',  # Add sentiment for sorting and display
            # Add role info for the owner
            'owner_id__userrole__role_id__name',
        )
        
        return queryset

    def filter_queryset(self, queryset):
        """
        Override filter_queryset to add secondary sorting by updated_on
        """
        queryset = super().filter_queryset(queryset)
        
        # Get ordering from query params
        # Ticket sorting
        ordering = self.request.query_params.get('ordering', '')
        
        # Default ordering
        if not ordering:
            return queryset.order_by('status_id__id', '-updated_on')
        
        # Check if this is a time field that should be reversed for intuitive sorting
        time_fields = ['updated_on', 'created_on']
        ordering_field = ordering.lstrip('-')  # Remove leading minus sign to get base field
        
        if ordering_field in time_fields:
            # For time fields, reverse the sorting direction for more intuitive behavior
            # If frontend sends 'updated_on' (ascending), we want descending (newer first)
            # If frontend sends '-updated_on' (descending), we want ascending (older first)
            if ordering.startswith('-'):
                # Frontend wants descending, give them ascending (older first)
                return queryset.order_by(ordering_field)
            else:
                # Frontend wants ascending, give them descending (newer first)
                return queryset.order_by(f'-{ordering_field}')

        # For other fields, add -updated_on as secondary sort
        return queryset.order_by(ordering, '-updated_on')

    def list(self, request, *args, **kwargs):
        """
        Override the default list method to format the response data.
        """
        # Get paginated queryset
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            # Prepare response format using the annotated sentiment data
            tickets = []
            for ticket in page:
                tickets.append({
                    'id': ticket['id'],
                    'status': {
                        'id': ticket['status_id__id'],
                        'name': ticket['status_id__name'],
                    },
                    'priority': {
                        'id': ticket['priority__id'],
                        'name': ticket['priority__name'],
                    },
                    'sentiment': ticket['latest_sentiment'],  # Use annotated sentiment
                    'customer': {
                        'name': ticket['customer_id__name'],
                        'email': ticket['customer_id__email'],
                    },
                    'owner': {
                        'id': ticket['owner_id__id'],
                        'name': ticket['owner_id__name'],
                        'username': ticket['owner_id__username'],
                        'role': ticket['owner_id__userrole__role_id__name'],
                    },
                    'platform_identity': {
                        'platform': ticket['platform_identity__platform'],
                        'channel_name': ticket['platform_identity__channel_name'],
                    },
                    'created_on': ticket['created_on'].isoformat(),
                    'updated_on': ticket['updated_on'].isoformat(),
                    'updated_ago': self._get_time_ago(ticket['updated_on']),
                })
            
            # Use the paginator's get_paginated_response
            return self.get_paginated_response(tickets)
        
        # If pagination is disabled, return all results
        return Response(tickets)
    
    def _get_time_ago(self, timestamp):
        """Helper method to calculate relative time for UI display"""
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        diff = now - timestamp
        
        if diff < timedelta(minutes=60):
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        elif diff < timedelta(hours=24):
            hours = int(diff.total_seconds() / 3600)
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif diff < timedelta(days=7):
            days = diff.days
            return f"{days} day{'s' if days != 1 else ''} ago"
        elif diff < timedelta(days=30):
            weeks = int(diff.days / 7)
            return f"{weeks} week{'s' if weeks != 1 else ''} ago"
        else:
            months = int(diff.days / 30)
            return f"{months} month{'s' if months != 1 else ''} ago"






        # statuses = [
        #     # {'name': 'default', 'definition': 'Default Status (should not be used)'},
        #     {'name': 'open', 'definition': 'Open Status - New ticket'},
        #     {'name': 'assigned', 'definition': 'Assigned Status - Ticket assigned to agent'},
        #     # {'name': 'in_progress', 'definition': 'In Progress - Being worked on'},
        #     {'name': 'waiting', 'definition': 'Waiting - Awaiting customer response'},
        #     # {'name': 'pending_to_close', 'definition': 'Pending to Close - Awaiting final confirmation'},
        #     {'name': 'closed', 'definition': 'Closed Status - Ticket completed'},
        #     # {'name': 'cancelled', 'definition': 'Cancelled - Ticket cancelled by customer'},
        # ]
        # for s in statuses:
        #     status = s['name']
        #     status_obj = Status.objects.filter(name=status)





















# TEMP-template
from django.shortcuts import render
def lobby(request):
    return render(request, 'chat/lobby.html')

def test_chat_01_version(request):
    # return render(request, 'chat/test_chat.html')
    return render(request, 'chat/test_chat_01_version.html')

def test_chat(request, ticket_id=None):
    if ticket_id is None:
        ticket_id = request.GET.get('ticket_id', '123')  # Default to 123 for testing
    context = {
        'ticket_id': ticket_id
    }
    return render(request, 'chat/test_chat.html', context)

