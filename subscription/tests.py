from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from setting.models import SystemSettings
from .services import SubscriptionDataService, QuotaValidationService

User = get_user_model()


class SubscriptionDataServiceTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            work_email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            name='Test User'
        )
        
        self.subscription_data = {
            "organization_name": "Test Organization",
            "subscription_key": "TEST-KEY123",
            "tier_id": "enterprise",
            "tier_name": "Enterprise",
            "status": "active",
            "activated_on": timezone.now().isoformat(),
            "expires_at": "2025-12-31T23:59:59Z",
            "quota": {
                "max_active_users": 20,
                "max_line_accounts": "unlimited",
                "max_ai_workflow_units": "unlimited",
                "max_messages_per_min": 100,
                "max_storage_gb": 1000,
            },
            "features": {
                "custom_transfer_algo": True,
                "ai_quick_reply": True,
                "crm_integration": False,
            },
            "metadata": {
                "billing_contact": "<EMAIL>",
                "technical_contact": "<EMAIL>",
            }
        }

    def test_get_subscription_data_not_exists(self):
        """Test getting subscription data when it doesn't exist"""
        result = SubscriptionDataService.get_subscription_data()
        self.assertIsNone(result)

    def test_save_and_get_subscription_data(self):
        """Test saving and retrieving subscription data"""
        # Save data
        saved_data = SubscriptionDataService.save_subscription_data(
            self.subscription_data, self.user
        )
        
        # Verify data was saved
        self.assertEqual(saved_data, self.subscription_data)
        
        # Retrieve data
        retrieved_data = SubscriptionDataService.get_subscription_data()
        self.assertEqual(retrieved_data, self.subscription_data)

    def test_has_active_subscription(self):
        """Test checking for active subscription"""
        # No subscription initially
        self.assertFalse(SubscriptionDataService.has_active_subscription())
        
        # Save active subscription
        SubscriptionDataService.save_subscription_data(self.subscription_data, self.user)
        self.assertTrue(SubscriptionDataService.has_active_subscription())
        
        # Test inactive subscription
        inactive_data = self.subscription_data.copy()
        inactive_data['status'] = 'expired'
        SubscriptionDataService.save_subscription_data(inactive_data, self.user)
        self.assertFalse(SubscriptionDataService.has_active_subscription())

    def test_is_subscription_expired(self):
        """Test checking if subscription is expired"""
        # No subscription data
        self.assertTrue(SubscriptionDataService.is_subscription_expired())
        
        # Active subscription
        SubscriptionDataService.save_subscription_data(self.subscription_data, self.user)
        self.assertFalse(SubscriptionDataService.is_subscription_expired())
        
        # Expired subscription
        expired_data = self.subscription_data.copy()
        expired_data['expires_at'] = "2020-12-31T23:59:59Z"
        SubscriptionDataService.save_subscription_data(expired_data, self.user)
        self.assertTrue(SubscriptionDataService.is_subscription_expired())


class QuotaValidationServiceTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            work_email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            name='Test User'
        )
        
        self.subscription_data = {
            "organization_name": "Test Organization",
            "tier_id": "enterprise",
            "tier_name": "Enterprise",
            "status": "active",
            "expires_at": "2025-12-31T23:59:59Z",
            "quota": {
                "max_active_users": 5,
            },
            "features": {
                "ai_quick_reply": True,
                "crm_integration": False,
            }
        }

    def test_can_create_user_no_subscription(self):
        """Test user creation validation with no subscription"""
        result = QuotaValidationService.can_create_user()
        self.assertFalse(result['allowed'])
        self.assertEqual(result['error'], 'No active subscription found')

    def test_can_create_user_within_quota(self):
        """Test user creation validation within quota"""
        SubscriptionDataService.save_subscription_data(self.subscription_data, self.user)
        
        result = QuotaValidationService.can_create_user()
        self.assertTrue(result['allowed'])
        self.assertIn('quota_info', result)
        self.assertEqual(result['quota_info']['max_users'], 5)

    def test_can_create_user_quota_exceeded(self):
        """Test user creation validation when quota is exceeded"""
        # Create subscription with max 2 users
        subscription_data = self.subscription_data.copy()
        subscription_data['quota']['max_active_users'] = 2
        SubscriptionDataService.save_subscription_data(subscription_data, self.user)
        
        # Create additional users to exceed quota
        for i in range(2):
            User.objects.create_user(
                username=f'user{i}',
                work_email=f'user{i}@example.com',
                password='testpass123',
                first_name=f'User{i}',
                last_name='Test',
                name=f'User{i} Test'
            )
        
        result = QuotaValidationService.can_create_user()
        self.assertFalse(result['allowed'])

    def test_check_feature_access(self):
        """Test feature access checking"""
        SubscriptionDataService.save_subscription_data(self.subscription_data, self.user)
        
        # Test enabled feature
        self.assertTrue(QuotaValidationService.check_feature_access('ai_quick_reply'))
        
        # Test disabled feature
        self.assertFalse(QuotaValidationService.check_feature_access('crm_integration'))
        
        # Test non-existent feature
        self.assertFalse(QuotaValidationService.check_feature_access('non_existent_feature'))


class SubscriptionAPITest(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            work_email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User',
            name='Test User',
            is_superuser=True
        )
        
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        self.subscription_data = {
            "organization_name": "Test Organization",
            "tier_id": "enterprise",
            "tier_name": "Enterprise",
            "status": "active",
            "expires_at": "2025-12-31T23:59:59Z",
            "quota": {
                "max_active_users": 10,
            },
            "features": {
                "ai_quick_reply": True,
            }
        }

    def test_quota_status_endpoint(self):
        """Test quota status API endpoint"""
        SubscriptionDataService.save_subscription_data(self.subscription_data, self.user)
        
        url = reverse('quota-status')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['organization_name'], 'Test Organization')

    def test_user_creation_validation_endpoint(self):
        """Test user creation validation API endpoint"""
        SubscriptionDataService.save_subscription_data(self.subscription_data, self.user)
        
        url = reverse('check-user-creation')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['data']['allowed'])

    def test_feature_access_endpoint(self):
        """Test feature access API endpoint"""
        SubscriptionDataService.save_subscription_data(self.subscription_data, self.user)
        
        url = reverse('check-feature-access')
        response = self.client.get(url, {'feature': 'ai_quick_reply'})
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['data']['has_access'])

    def test_subscription_info_endpoint(self):
        """Test subscription info API endpoint"""
        SubscriptionDataService.save_subscription_data(self.subscription_data, self.user)
        
        url = reverse('subscription-info')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['organization_name'], 'Test Organization')
